/* 主题样式定义 */

/* 明亮主题 */
.theme-light {
  --bg-color: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --text-color: #333333;
  --text-secondary: #666666;
  --text-muted: #999999;
  --border-color: #e0e0e0;
  --border-light: #f0f0f0;
  --primary-color: #007acc;
  --primary-hover: #005a9e;
  --primary-light: rgba(0, 122, 204, 0.1);
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.15);
  --font-family: 'Microsoft YaHei', 'SimSun', sans-serif;
  
  /* 编辑器特定颜色 */
  --editor-bg: #ffffff;
  --editor-text: #333333;
  --editor-selection: rgba(0, 122, 204, 0.2);
  --editor-cursor: #333333;
  --editor-line-highlight: rgba(0, 122, 204, 0.05);
}

/* 暗黑主题 */
.theme-dark {
  --bg-color: #1e1e1e;
  --bg-secondary: #252526;
  --bg-tertiary: #2d2d30;
  --text-color: #d4d4d4;
  --text-secondary: #969696;
  --text-muted: #6a6a6a;
  --border-color: #404040;
  --border-light: #333333;
  --primary-color: #0078d4;
  --primary-hover: #106ebe;
  --primary-light: rgba(0, 120, 212, 0.2);
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --danger-color: #f44336;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  --shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.4);
  --font-family: 'Microsoft YaHei', 'SimSun', sans-serif;
  
  /* 编辑器特定颜色 */
  --editor-bg: #1e1e1e;
  --editor-text: #d4d4d4;
  --editor-selection: rgba(0, 120, 212, 0.3);
  --editor-cursor: #d4d4d4;
  --editor-line-highlight: rgba(255, 255, 255, 0.05);
}

/* 护眼主题 */
.theme-sepia {
  --bg-color: #f7f3e9;
  --bg-secondary: #f0ead6;
  --bg-tertiary: #e8dcc0;
  --text-color: #5c4b37;
  --text-secondary: #8b7355;
  --text-muted: #a68b5b;
  --border-color: #d3c7b8;
  --border-light: #e0d4c5;
  --primary-color: #8b4513;
  --primary-hover: #a0522d;
  --primary-light: rgba(139, 69, 19, 0.1);
  --success-color: #6b8e23;
  --warning-color: #daa520;
  --danger-color: #cd5c5c;
  --shadow: 0 2px 8px rgba(92, 75, 55, 0.1);
  --shadow-hover: 0 4px 16px rgba(92, 75, 55, 0.15);
  --font-family: 'Microsoft YaHei', 'SimSun', serif;
  
  /* 编辑器特定颜色 */
  --editor-bg: #f7f3e9;
  --editor-text: #5c4b37;
  --editor-selection: rgba(139, 69, 19, 0.2);
  --editor-cursor: #5c4b37;
  --editor-line-highlight: rgba(139, 69, 19, 0.05);
}

/* 主题过渡动画 */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 滚动条主题适配 */
.theme-light ::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.theme-light ::-webkit-scrollbar-thumb {
  background: var(--border-color);
}

.theme-light ::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

.theme-dark ::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.theme-dark ::-webkit-scrollbar-thumb {
  background: var(--border-color);
}

.theme-dark ::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

.theme-sepia ::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.theme-sepia ::-webkit-scrollbar-thumb {
  background: var(--border-color);
}

.theme-sepia ::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* 选择文本主题适配 */
.theme-light ::selection {
  background: var(--primary-color);
  color: white;
}

.theme-dark ::selection {
  background: var(--primary-color);
  color: white;
}

.theme-sepia ::selection {
  background: var(--primary-color);
  color: white;
}

/* 焦点状态主题适配 */
.theme-light input:focus,
.theme-light textarea:focus,
.theme-light select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-light);
}

.theme-dark input:focus,
.theme-dark textarea:focus,
.theme-dark select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-light);
}

.theme-sepia input:focus,
.theme-sepia textarea:focus,
.theme-sepia select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-light);
}

/* 按钮主题适配 */
.btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
}

.btn-primary:hover {
  background: var(--primary-hover);
}

.btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--bg-tertiary);
}

.btn-danger {
  background: var(--danger-color);
  color: white;
  border: none;
}

.btn-danger:hover {
  background: color-mix(in srgb, var(--danger-color) 80%, black);
}

/* 卡片主题适配 */
.card {
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow);
}

.card:hover {
  box-shadow: var(--shadow-hover);
}

/* 输入框主题适配 */
input,
textarea,
select {
  background: var(--bg-secondary);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

input::placeholder,
textarea::placeholder {
  color: var(--text-muted);
}

/* 分隔线主题适配 */
.divider {
  border-color: var(--border-color);
}

/* 状态徽章主题适配 */
.badge {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.badge-primary {
  background: var(--primary-light);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.badge-success {
  background: color-mix(in srgb, var(--success-color) 20%, transparent);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.badge-warning {
  background: color-mix(in srgb, var(--warning-color) 20%, transparent);
  color: var(--warning-color);
  border: 1px solid var(--warning-color);
}

.badge-danger {
  background: color-mix(in srgb, var(--danger-color) 20%, transparent);
  color: var(--danger-color);
  border: 1px solid var(--danger-color);
}

/* 工具提示主题适配 */
.tooltip {
  background: var(--bg-tertiary);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow);
}

/* 下拉菜单主题适配 */
.dropdown {
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow);
}

.dropdown-item:hover {
  background: var(--bg-secondary);
}

/* 模态框主题适配 */
.modal-overlay {
  background: rgba(0, 0, 0, 0.5);
}

.theme-dark .modal-overlay {
  background: rgba(0, 0, 0, 0.7);
}

.modal {
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-hover);
}

/* 加载动画主题适配 */
.loading-spinner {
  border-color: var(--border-color);
  border-top-color: var(--primary-color);
}

/* 进度条主题适配 */
.progress-bar {
  background: var(--bg-secondary);
}

.progress-fill {
  background: var(--primary-color);
}

/* 代码块主题适配 */
.code {
  background: var(--bg-secondary);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

/* 表格主题适配 */
.table {
  background: var(--bg-color);
  color: var(--text-color);
}

.table th {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.table td {
  border-bottom: 1px solid var(--border-light);
}

.table tr:hover {
  background: var(--bg-secondary);
}

/* 标签页主题适配 */
.tab {
  color: var(--text-secondary);
  border-bottom: 2px solid transparent;
}

.tab:hover {
  color: var(--text-color);
}

.tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

/* 面包屑导航主题适配 */
.breadcrumb {
  color: var(--text-secondary);
}

.breadcrumb-separator {
  color: var(--text-muted);
}

.breadcrumb-current {
  color: var(--text-color);
}

/* 侧边栏主题适配 */
.sidebar {
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
}

.sidebar-item {
  color: var(--text-secondary);
}

.sidebar-item:hover {
  background: var(--bg-color);
  color: var(--text-color);
}

.sidebar-item.active {
  background: var(--primary-light);
  color: var(--primary-color);
}

/* 编辑器特定样式 */
.editor-container {
  background: var(--editor-bg);
  color: var(--editor-text);
}

.editor-line-highlight {
  background: var(--editor-line-highlight);
}

.editor-selection {
  background: var(--editor-selection);
}

.editor-cursor {
  border-left: 2px solid var(--editor-cursor);
}

/* 心流模式样式 */
.zen-mode {
  background: var(--editor-bg);
}

.zen-mode * {
  color: var(--editor-text);
}

/* 专注模式样式 */
.focus-mode .editor-content {
  background: var(--editor-bg);
}

.focus-mode .editor-content > *:not(.current-paragraph) {
  opacity: 0.3;
}

/* 打字机模式样式 */
.typewriter-mode .editor-textarea {
  padding-top: 50vh;
  padding-bottom: 50vh;
}

/* 响应式主题适配 */
@media (max-width: 768px) {
  .theme-light,
  .theme-dark,
  .theme-sepia {
    --shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .theme-light {
    --border-color: #000000;
    --text-secondary: #000000;
  }
  
  .theme-dark {
    --border-color: #ffffff;
    --text-secondary: #ffffff;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}
