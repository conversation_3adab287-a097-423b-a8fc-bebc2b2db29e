import{d as V,c as b,o as v,a as t,w as C,b as w,t as d,n as _,_ as D,r as g,e as B,f as M,g as N,h as $,v as h,i as H,u as T,j as L,k as E,F as q,l as Z,m as x}from"./main-CHDPsXFQ.js";const W={class:"book-cover"},j=["src","alt"],O={key:1,class:"default-cover"},Q={class:"book-actions"},R={class:"book-info"},G={class:"book-title"},J={class:"book-author"},K={class:"book-stats"},P={class:"stat-item"},X={class:"stat-value"},Y={class:"stat-item"},tt={class:"stat-value"},ot={class:"book-status"},et={class:"update-time"},st={key:0,class:"book-description"},lt=V({__name:"BookCard",props:{book:{}},emits:["open","edit","delete"],setup(y){function p(o){return o>=1e4?`${(o/1e4).toFixed(1)}万`:o.toString()}function s(o){return{draft:"草稿",writing:"连载中",completed:"已完结",published:"已发布"}[o]||o}function u(o){const e=new Date().getTime()-new Date(o).getTime(),a=Math.floor(e/(1e3*60*60*24));return a===0?"今天更新":a===1?"昨天更新":a<7?`${a}天前更新`:new Date(o).toLocaleDateString("zh-CN",{month:"short",day:"numeric"})}return(o,i)=>(v(),b("div",{class:"book-card",onClick:i[2]||(i[2]=e=>o.$emit("open",o.book))},[t("div",W,[o.book.coverImage?(v(),b("img",{key:0,src:o.book.coverImage,alt:o.book.title},null,8,j)):(v(),b("div",O,i[3]||(i[3]=[t("svg",{width:"48",height:"48",viewBox:"0 0 24 24"},[t("path",{d:"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z",fill:"currentColor"})],-1)]))),t("div",Q,[t("button",{class:"action-btn edit-btn",onClick:i[0]||(i[0]=C(e=>o.$emit("edit",o.book),["stop"])),title:"编辑"},i[4]||(i[4]=[t("svg",{width:"16",height:"16",viewBox:"0 0 24 24"},[t("path",{d:"M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z",fill:"currentColor"})],-1)])),t("button",{class:"action-btn delete-btn",onClick:i[1]||(i[1]=C(e=>o.$emit("delete",o.book),["stop"])),title:"删除"},i[5]||(i[5]=[t("svg",{width:"16",height:"16",viewBox:"0 0 24 24"},[t("path",{d:"M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z",fill:"currentColor"})],-1)]))])]),t("div",R,[t("h3",G,d(o.book.title),1),t("p",J,d(o.book.author),1),t("div",K,[t("div",P,[i[6]||(i[6]=t("span",{class:"stat-label"},"字数",-1)),t("span",X,d(p(o.book.wordCount)),1)]),t("div",Y,[i[7]||(i[7]=t("span",{class:"stat-label"},"章节",-1)),t("span",tt,d(o.book.chapterCount),1)])]),t("div",ot,[t("span",{class:_(["status-badge",`status-${o.book.status}`])},d(s(o.book.status)),3),t("span",et,d(u(o.book.updatedAt)),1)]),o.book.description?(v(),b("p",st,d(o.book.description),1)):w("",!0)])]))}}),it=D(lt,[["__scopeId","data-v-66abea87"]]),nt={class:"dialog-header"},at={class:"form-group"},rt={class:"form-group"},dt={class:"form-group"},ut={class:"char-count"},vt={class:"form-group"},pt={class:"dialog-actions"},mt=["disabled"],bt=V({__name:"NewBookDialog",emits:["close","create"],setup(y){const p=g(),s=g({title:"",author:"",description:"",status:"draft",wordCount:0,chapterCount:0}),u=B(()=>s.value.title.trim()&&s.value.author.trim());function o(){if(!u.value)return;const i={title:s.value.title.trim(),author:s.value.author.trim(),description:s.value.description.trim()||void 0,status:s.value.status,wordCount:0,chapterCount:0};emit("create",i)}return M(async()=>{var i;await N(),(i=p.value)==null||i.focus()}),(i,e)=>(v(),b("div",{class:"dialog-overlay",onClick:e[7]||(e[7]=a=>i.$emit("close"))},[t("div",{class:"dialog",onClick:e[6]||(e[6]=C(()=>{},["stop"]))},[t("div",nt,[e[9]||(e[9]=t("h2",null,"新建书籍",-1)),t("button",{class:"close-btn",onClick:e[0]||(e[0]=a=>i.$emit("close"))},e[8]||(e[8]=[t("svg",{width:"20",height:"20",viewBox:"0 0 24 24"},[t("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z",fill:"currentColor"})],-1)]))]),t("form",{onSubmit:C(o,["prevent"]),class:"dialog-content"},[t("div",at,[e[10]||(e[10]=t("label",{for:"title"},"书名 *",-1)),$(t("input",{id:"title","onUpdate:modelValue":e[1]||(e[1]=a=>s.value.title=a),type:"text",placeholder:"请输入书名",required:"",maxlength:"100",ref_key:"titleInput",ref:p},null,512),[[h,s.value.title]])]),t("div",rt,[e[11]||(e[11]=t("label",{for:"author"},"作者 *",-1)),$(t("input",{id:"author","onUpdate:modelValue":e[2]||(e[2]=a=>s.value.author=a),type:"text",placeholder:"请输入作者名",required:"",maxlength:"50"},null,512),[[h,s.value.author]])]),t("div",dt,[e[12]||(e[12]=t("label",{for:"description"},"简介",-1)),$(t("textarea",{id:"description","onUpdate:modelValue":e[3]||(e[3]=a=>s.value.description=a),placeholder:"请输入书籍简介（可选）",rows:"4",maxlength:"500"},null,512),[[h,s.value.description]]),t("div",ut,d(s.value.description.length)+"/500",1)]),t("div",vt,[e[14]||(e[14]=t("label",{for:"status"},"状态",-1)),$(t("select",{id:"status","onUpdate:modelValue":e[4]||(e[4]=a=>s.value.status=a)},e[13]||(e[13]=[t("option",{value:"draft"},"草稿",-1),t("option",{value:"writing"},"连载中",-1),t("option",{value:"completed"},"已完结",-1),t("option",{value:"published"},"已发布",-1)]),512),[[H,s.value.status]])]),t("div",pt,[t("button",{type:"button",class:"btn btn-secondary",onClick:e[5]||(e[5]=a=>i.$emit("close"))}," 取消 "),t("button",{type:"submit",class:"btn btn-primary",disabled:!u.value}," 创建 ",8,mt)])],32)])]))}}),kt=D(bt,[["__scopeId","data-v-115341ba"]]),ct={class:"dialog-header"},ft={class:"form-group"},gt={class:"form-group"},$t={class:"form-group"},ht={class:"char-count"},Ct={class:"form-group"},yt={class:"book-stats"},wt={class:"stat-item"},Bt={class:"stat-value"},Vt={class:"stat-item"},Dt={class:"stat-value"},Lt={class:"stat-item"},Mt={class:"stat-value"},Ht={class:"stat-item"},St={class:"stat-value"},zt={class:"dialog-actions"},At=["disabled"],Ut=V({__name:"EditBookDialog",props:{book:{}},emits:["close","update"],setup(y,{emit:p}){const s=y,u=p,o=g({title:"",author:"",description:"",status:"draft"}),i=B(()=>o.value.title.trim()&&o.value.author.trim());function e(){if(!i.value)return;const r={title:o.value.title.trim(),author:o.value.author.trim(),description:o.value.description.trim()||void 0,status:o.value.status};u("update",r)}function a(r){return r>=1e4?`${(r/1e4).toFixed(1)}万`:r.toString()}function c(r){return new Date(r).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric"})}return M(()=>{o.value={title:s.book.title,author:s.book.author,description:s.book.description||"",status:s.book.status}}),(r,l)=>(v(),b("div",{class:"dialog-overlay",onClick:l[7]||(l[7]=m=>r.$emit("close"))},[t("div",{class:"dialog",onClick:l[6]||(l[6]=C(()=>{},["stop"]))},[t("div",ct,[l[9]||(l[9]=t("h2",null,"编辑书籍",-1)),t("button",{class:"close-btn",onClick:l[0]||(l[0]=m=>r.$emit("close"))},l[8]||(l[8]=[t("svg",{width:"20",height:"20",viewBox:"0 0 24 24"},[t("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z",fill:"currentColor"})],-1)]))]),t("form",{onSubmit:C(e,["prevent"]),class:"dialog-content"},[t("div",ft,[l[10]||(l[10]=t("label",{for:"title"},"书名 *",-1)),$(t("input",{id:"title","onUpdate:modelValue":l[1]||(l[1]=m=>o.value.title=m),type:"text",placeholder:"请输入书名",required:"",maxlength:"100"},null,512),[[h,o.value.title]])]),t("div",gt,[l[11]||(l[11]=t("label",{for:"author"},"作者 *",-1)),$(t("input",{id:"author","onUpdate:modelValue":l[2]||(l[2]=m=>o.value.author=m),type:"text",placeholder:"请输入作者名",required:"",maxlength:"50"},null,512),[[h,o.value.author]])]),t("div",$t,[l[12]||(l[12]=t("label",{for:"description"},"简介",-1)),$(t("textarea",{id:"description","onUpdate:modelValue":l[3]||(l[3]=m=>o.value.description=m),placeholder:"请输入书籍简介（可选）",rows:"4",maxlength:"500"},null,512),[[h,o.value.description]]),t("div",ht,d(o.value.description.length)+"/500",1)]),t("div",Ct,[l[14]||(l[14]=t("label",{for:"status"},"状态",-1)),$(t("select",{id:"status","onUpdate:modelValue":l[4]||(l[4]=m=>o.value.status=m)},l[13]||(l[13]=[t("option",{value:"draft"},"草稿",-1),t("option",{value:"writing"},"连载中",-1),t("option",{value:"completed"},"已完结",-1),t("option",{value:"published"},"已发布",-1)]),512),[[H,o.value.status]])]),t("div",yt,[t("div",wt,[l[15]||(l[15]=t("span",{class:"stat-label"},"字数",-1)),t("span",Bt,d(a(r.book.wordCount)),1)]),t("div",Vt,[l[16]||(l[16]=t("span",{class:"stat-label"},"章节",-1)),t("span",Dt,d(r.book.chapterCount),1)]),t("div",Lt,[l[17]||(l[17]=t("span",{class:"stat-label"},"创建时间",-1)),t("span",Mt,d(c(r.book.createdAt)),1)]),t("div",Ht,[l[18]||(l[18]=t("span",{class:"stat-label"},"更新时间",-1)),t("span",St,d(c(r.book.updatedAt)),1)])]),t("div",zt,[t("button",{type:"button",class:"btn btn-secondary",onClick:l[5]||(l[5]=m=>r.$emit("close"))}," 取消 "),t("button",{type:"submit",class:"btn btn-primary",disabled:!i.value}," 保存 ",8,At)])],32)])]))}}),Ft=D(Ut,[["__scopeId","data-v-889709c9"]]),It={class:"dialog-header"},_t={class:"dialog-content"},Nt={class:"dialog-actions"},Tt=V({__name:"ConfirmDialog",props:{title:{},message:{}},emits:["confirm","cancel"],setup(y){return(p,s)=>(v(),b("div",{class:"dialog-overlay",onClick:s[3]||(s[3]=u=>p.$emit("cancel"))},[t("div",{class:"dialog",onClick:s[2]||(s[2]=C(()=>{},["stop"]))},[t("div",It,[s[4]||(s[4]=t("div",{class:"dialog-icon"},[t("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor"},[t("path",{d:"M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"})])],-1)),t("h2",null,d(p.title),1)]),t("div",_t,[t("p",null,d(p.message),1)]),t("div",Nt,[t("button",{type:"button",class:"btn btn-secondary",onClick:s[0]||(s[0]=u=>p.$emit("cancel"))}," 取消 "),t("button",{type:"button",class:"btn btn-danger",onClick:s[1]||(s[1]=u=>p.$emit("confirm"))}," 确定 ")])])]))}}),Et=D(Tt,[["__scopeId","data-v-88d65cb3"]]),qt={class:"library"},Zt={class:"library-header"},xt={class:"header-left"},Wt={class:"book-count"},jt={class:"header-right"},Ot={class:"search-box"},Qt={class:"library-content"},Rt={key:0,class:"loading-state"},Gt={key:1,class:"empty-state"},Jt={key:2,class:"books-grid"},Kt=V({__name:"Library",setup(y){const p=x(),s=T(),u=g(""),o=g(!1),i=g(!1),e=g(!1),a=g(null),c=g(null),r=B(()=>s.books),l=B(()=>s.isLoading),m=B(()=>u.value.trim()?s.searchBooks(u.value):r.value);async function S(k){try{const n=await s.createBook(k);o.value=!1,n&&p.push(`/editor/${n.id}`)}catch(n){console.error("Failed to create book:",n)}}function z(k){a.value=k,i.value=!0}async function A(k){if(a.value)try{await s.updateBook(a.value.id,k),i.value=!1,a.value=null}catch(n){console.error("Failed to update book:",n)}}function U(k){c.value=k,e.value=!0}async function F(){if(c.value)try{await s.deleteBook(c.value.id),e.value=!1,c.value=null}catch(k){console.error("Failed to delete book:",k)}}function I(k){p.push(`/editor/${k.id}`)}return M(async()=>{await s.loadBooks()}),(k,n)=>(v(),b("div",qt,[t("div",Zt,[t("div",xt,[n[6]||(n[6]=t("h1",{class:"library-title"},"我的书架",-1)),t("span",Wt,d(r.value.length)+" 本书籍",1)]),t("div",jt,[t("div",Ot,[$(t("input",{"onUpdate:modelValue":n[0]||(n[0]=f=>u.value=f),type:"text",placeholder:"搜索书籍...",class:"search-input"},null,512),[[h,u.value]]),n[7]||(n[7]=t("svg",{class:"search-icon",width:"16",height:"16",viewBox:"0 0 24 24"},[t("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z",fill:"currentColor"})],-1))]),t("button",{class:"new-book-btn",onClick:n[1]||(n[1]=f=>o.value=!0)},n[8]||(n[8]=[t("svg",{width:"16",height:"16",viewBox:"0 0 24 24"},[t("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z",fill:"currentColor"})],-1),E(" 新建书籍 ",-1)]))])]),t("div",Qt,[l.value?(v(),b("div",Rt,n[9]||(n[9]=[t("div",{class:"loading-spinner"},null,-1),t("p",null,"加载中...",-1)]))):m.value.length===0?(v(),b("div",Gt,[n[10]||(n[10]=t("svg",{class:"empty-icon",width:"64",height:"64",viewBox:"0 0 24 24"},[t("path",{d:"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z",fill:"currentColor"})],-1)),t("h3",null,d(u.value?"没有找到匹配的书籍":"还没有书籍"),1),t("p",null,d(u.value?"尝试使用其他关键词搜索":'点击"新建书籍"开始创作吧！'),1),u.value?w("",!0):(v(),b("button",{key:0,class:"new-book-btn",onClick:n[2]||(n[2]=f=>o.value=!0)}," 新建书籍 "))])):(v(),b("div",Jt,[(v(!0),b(q,null,Z(m.value,f=>(v(),L(it,{key:f.id,book:f,onEdit:z,onDelete:U,onOpen:I},null,8,["book"]))),128))]))]),o.value?(v(),L(kt,{key:0,onClose:n[3]||(n[3]=f=>o.value=!1),onCreate:S})):w("",!0),i.value&&a.value?(v(),L(Ft,{key:1,book:a.value,onClose:n[4]||(n[4]=f=>i.value=!1),onUpdate:A},null,8,["book"])):w("",!0),e.value&&c.value?(v(),L(Et,{key:2,title:"删除书籍",message:`确定要删除《${c.value.title}》吗？此操作不可撤销。`,onConfirm:F,onCancel:n[5]||(n[5]=f=>e.value=!1)},null,8,["message"])):w("",!0)]))}}),Xt=D(Kt,[["__scopeId","data-v-24ab0d3c"]]);export{Xt as default};
