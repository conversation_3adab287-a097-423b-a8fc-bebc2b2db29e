# Novel Writer

一个现代化的网文写作编辑器，基于 Electron + TypeScript + Vue 3 构建。

## 特性

### 🎨 现代化界面
- 简洁优雅的设计
- 支持明亮、暗黑、护眼三种主题
- 响应式布局，适配不同屏幕尺寸

### 📚 书架管理
- 直观的书籍管理界面
- 支持新建、编辑、删除书籍
- 书籍搜索功能
- 显示字数、章节数等统计信息

### ✍️ 强大的编辑器
- 章节式管理，便于组织内容
- 实时字数统计
- 自动保存功能
- 心流模式，专注写作
- 打字机模式
- 专注模式

### ⚙️ 个性化设置
- 自定义字体、字号、行高
- 可调节的编辑器配色
- 多种编辑模式切换
- 自动保存间隔设置

### 💾 数据安全
- 本地数据存储
- 自动备份
- 支持导出为 TXT、Word、PDF 格式

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **桌面应用**: Electron
- **状态管理**: Pinia
- **路由**: Vue Router
- **构建工具**: Vite
- **数据存储**: electron-store

## 开发环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

## 安装和运行

### 克隆项目
```bash
git clone https://github.com/your-username/novel-writer.git
cd novel-writer
```

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建应用
```bash
npm run build
```

### 打包应用
```bash
npm run dist
```

## 项目结构

```
novel-writer/
├── src/
│   ├── main/                 # Electron 主进程
│   │   ├── main.ts          # 主进程入口
│   │   ├── services/        # 主进程服务
│   │   └── preload/         # 预加载脚本
│   ├── renderer/            # 渲染进程
│   │   ├── src/
│   │   │   ├── components/  # Vue 组件
│   │   │   ├── views/       # 页面视图
│   │   │   ├── stores/      # Pinia 状态管理
│   │   │   ├── router/      # 路由配置
│   │   │   └── styles/      # 样式文件
│   │   └── index.html       # 渲染进程入口
│   └── shared/              # 共享类型和常量
├── dist/                    # 构建输出
├── release/                 # 打包输出
└── package.json
```

## 主要功能模块

### 书架管理 (Library)
- 书籍列表展示
- 新建书籍对话框
- 书籍编辑功能
- 书籍删除确认
- 搜索过滤

### 编辑器 (Editor)
- 章节列表侧边栏
- 文本编辑区域
- 工具栏功能
- 心流模式
- 自动保存

### 设置 (Settings)
- 编辑器设置
- 应用设置
- 主题切换
- 关于信息

## 开发指南

### 添加新功能
1. 在 `src/shared/types.ts` 中定义相关类型
2. 在主进程中添加 IPC 处理器
3. 在渲染进程中创建对应的组件和页面
4. 更新状态管理和路由配置

### 主题定制
主题样式定义在 `src/renderer/src/styles/themes.css` 中，支持：
- CSS 变量定义
- 多主题切换
- 响应式适配
- 无障碍支持

### 数据存储
使用 electron-store 进行本地数据持久化：
- 书籍数据存储在 `books.json`
- 章节数据存储在 `chapters.json`
- 用户设置存储在 `settings.json`

## 构建和发布

### 开发构建
```bash
npm run build
```

### 生产构建
```bash
npm run dist
```

支持的平台：
- Windows (NSIS 安装包)
- macOS (DMG 镜像)
- Linux (AppImage)

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 致谢

- [Electron](https://electronjs.org/) - 跨平台桌面应用框架
- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [TypeScript](https://www.typescriptlang.org/) - JavaScript 的超集
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
- [Pinia](https://pinia.vuejs.org/) - Vue 状态管理库

## 联系方式

- 项目主页: [https://github.com/your-username/novel-writer](https://github.com/your-username/novel-writer)
- 问题反馈: [https://github.com/your-username/novel-writer/issues](https://github.com/your-username/novel-writer/issues)

---

**Novel Writer** - 让写作更简单，让创作更专注。
