/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/

define("vs/base/common/worker/simpleWorker.nls.fr", {
	"vs/base/common/platform": [
		"_",
	],
	"vs/editor/common/languages": [
		"tableau",
		"booléen",
		"classe",
		"constante",
		"constructeur",
		"énumération",
		"membre d\'énumération",
		"événement",
		"champ",
		"fichier",
		"fonction",
		"interface",
		"clé",
		"méthode",
		"module",
		"espace de noms",
		"NULL",
		"nombre",
		"objet",
		"opérateur",
		"package",
		"propriété",
		"chaîne",
		"struct",
		"paramètre de type",
		"variable",
		"{0} ({1})",
	]
});