import { BrowserWindow, screen } from 'electron'
import { join } from 'path'
import { DEFAULT_APP_SETTINGS } from '../../shared/constants'

export class WindowManager {
  private mainWindow: BrowserWindow | null = null

  async createMainWindow(): Promise<BrowserWindow> {
    const { width, height } = screen.getPrimaryDisplay().workAreaSize
    
    // 创建浏览器窗口
    this.mainWindow = new BrowserWindow({
      width: Math.min(DEFAULT_APP_SETTINGS.windowSize.width, width),
      height: Math.min(DEFAULT_APP_SETTINGS.windowSize.height, height),
      minWidth: 800,
      minHeight: 600,
      show: false,
      titleBarStyle: 'default',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: join(__dirname, '../preload/preload.js'),
        webSecurity: true
      },
      icon: this.getAppIcon()
    })

    // 加载应用
    if (process.argv.includes('--dev')) {
      // 开发模式
      await this.mainWindow.loadURL('http://localhost:3000')
    } else {
      // 生产模式
      await this.mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
    }

    // 窗口准备好后显示
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show()
      
      // 居中显示
      this.mainWindow?.center()
    })

    // 窗口关闭事件
    this.mainWindow.on('closed', () => {
      this.mainWindow = null
    })

    // 防止新窗口打开
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      // 在默认浏览器中打开外部链接
      if (url.startsWith('http://') || url.startsWith('https://')) {
        require('electron').shell.openExternal(url)
      }
      return { action: 'deny' }
    })

    return this.mainWindow
  }

  getMainWindow(): BrowserWindow | null {
    return this.mainWindow
  }

  private getAppIcon(): string | undefined {
    // 根据平台返回相应的图标路径
    if (process.platform === 'win32') {
      return join(__dirname, '../../assets/icon.ico')
    } else if (process.platform === 'darwin') {
      return join(__dirname, '../../assets/icon.icns')
    } else {
      return join(__dirname, '../../assets/icon.png')
    }
  }

  // 窗口控制方法
  minimize() {
    this.mainWindow?.minimize()
  }

  maximize() {
    if (this.mainWindow?.isMaximized()) {
      this.mainWindow.unmaximize()
    } else {
      this.mainWindow?.maximize()
    }
  }

  close() {
    this.mainWindow?.close()
  }

  toggleFullscreen() {
    const isFullscreen = this.mainWindow?.isFullScreen() || false
    this.mainWindow?.setFullScreen(!isFullscreen)
  }

  // 获取窗口状态
  isMaximized(): boolean {
    return this.mainWindow?.isMaximized() || false
  }

  isMinimized(): boolean {
    return this.mainWindow?.isMinimized() || false
  }

  isFullscreen(): boolean {
    return this.mainWindow?.isFullScreen() || false
  }
}
