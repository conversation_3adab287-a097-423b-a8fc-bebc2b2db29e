import{d as B,p as T,r as F,e as C,f as N,c as o,a as t,k as m,F as b,l as f,b as g,h as u,i as y,q as Z,v as c,t as r,s as p,m as E,o as i,n as M,x as D,y as G,_ as $}from"./main-CHDPsXFQ.js";const q={class:"settings-page"},O={class:"settings-content"},P={class:"settings-sidebar"},R={class:"settings-nav"},W=["onClick"],j={width:"16",height:"16",viewBox:"0 0 24 24"},J=["d"],K={class:"settings-main"},Q={key:0,class:"settings-section"},X={class:"setting-group"},Y={class:"theme-grid"},t1=["onClick"],e1={class:"setting-group"},s1={class:"setting-item"},n1=["value"],l1={class:"setting-item"},o1={class:"slider-control"},i1={class:"setting-item"},a1={class:"slider-control"},u1={class:"setting-group"},d1={class:"setting-item"},r1={class:"toggle-switch"},v1={class:"setting-item"},g1={class:"toggle-switch"},p1={class:"setting-item"},C1={class:"toggle-switch"},m1={key:1,class:"settings-section"},b1={class:"setting-group"},f1={class:"setting-item"},c1={class:"toggle-switch"},S1={key:0,class:"setting-item"},w1={class:"slider-control"},y1={class:"setting-group"},M1={class:"setting-item"},k1={key:2,class:"settings-section"},L1=B({__name:"Settings",setup(V1){const k=E(),a=T(),v=F("editor"),L=[{id:"editor",name:"编辑器",icon:"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"},{id:"app",name:"应用",icon:"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"},{id:"about",name:"关于",icon:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z"}],V=[{name:"明亮",value:"light",preview:{background:"#ffffff",border:"2px solid #e0e0e0"}},{name:"暗黑",value:"dark",preview:{background:"#1e1e1e",border:"2px solid #404040"}},{name:"护眼",value:"sepia",preview:{background:"#f7f3e9",border:"2px solid #d3c7b8"}}],h=G,H=C(()=>a.currentTheme),n=C(()=>({...a.editorSettings})),l=C(()=>({...a.settings}));function x(){k.go(-1)}async function A(w){await a.setTheme(w)}async function d(){await a.updateEditorSettings(n.value)}async function S(){await a.updateSettings(l.value)}async function _(){await a.setAutoSaveInterval(l.value.autoSaveInterval)}function I(){window.electronAPI||window.open("https://github.com/novel-writer","_blank")}async function z(){confirm("确定要重置所有设置吗？此操作不可撤销。")&&await a.resetSettings()}return N(async()=>{await a.loadSettings()}),(w,e)=>(i(),o("div",q,[t("div",{class:"settings-header"},[t("button",{onClick:x,class:"back-btn"},e[9]||(e[9]=[t("svg",{width:"16",height:"16",viewBox:"0 0 24 24"},[t("path",{d:"M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z",fill:"currentColor"})],-1),m(" 返回 ",-1)])),e[10]||(e[10]=t("h1",null,"设置",-1))]),t("div",O,[t("div",P,[t("nav",R,[(i(),o(b,null,f(L,s=>t("button",{key:s.id,class:M(["nav-item",{active:v.value===s.id}]),onClick:U=>v.value=s.id},[(i(),o("svg",j,[t("path",{d:s.icon,fill:"currentColor"},null,8,J)])),m(" "+r(s.name),1)],10,W)),64))])]),t("div",K,[v.value==="editor"?(i(),o("div",Q,[e[23]||(e[23]=t("h2",null,"编辑器设置",-1)),t("div",X,[e[11]||(e[11]=t("h3",null,"主题",-1)),t("div",Y,[(i(),o(b,null,f(V,s=>t("button",{key:s.value,class:M(["theme-card",{active:H.value===s.value}]),onClick:U=>A(s.value)},[t("div",{class:"theme-preview",style:D(s.preview)},null,4),t("span",null,r(s.name),1)],10,t1)),64))])]),t("div",e1,[e[15]||(e[15]=t("h3",null,"字体",-1)),t("div",s1,[e[12]||(e[12]=t("label",null,"字体族",-1)),u(t("select",{"onUpdate:modelValue":e[0]||(e[0]=s=>n.value.fontFamily=s),onChange:d},[(i(!0),o(b,null,f(Z(h),s=>(i(),o("option",{key:s.value,value:s.value},r(s.name),9,n1))),128))],544),[[y,n.value.fontFamily]])]),t("div",l1,[e[13]||(e[13]=t("label",null,"字体大小",-1)),t("div",o1,[u(t("input",{type:"range",min:"12",max:"24","onUpdate:modelValue":e[1]||(e[1]=s=>n.value.fontSize=s),onInput:d},null,544),[[c,n.value.fontSize,void 0,{number:!0}]]),t("span",null,r(n.value.fontSize)+"px",1)])]),t("div",i1,[e[14]||(e[14]=t("label",null,"行高",-1)),t("div",a1,[u(t("input",{type:"range",min:"1.2",max:"2.5",step:"0.1","onUpdate:modelValue":e[2]||(e[2]=s=>n.value.lineHeight=s),onInput:d},null,544),[[c,n.value.lineHeight,void 0,{number:!0}]]),t("span",null,r(n.value.lineHeight),1)])])]),t("div",u1,[e[22]||(e[22]=t("h3",null,"编辑模式",-1)),t("div",d1,[e[17]||(e[17]=t("label",null,"心流模式",-1)),t("div",r1,[u(t("input",{type:"checkbox",id:"zenMode","onUpdate:modelValue":e[3]||(e[3]=s=>n.value.zenMode=s),onChange:d},null,544),[[p,n.value.zenMode]]),e[16]||(e[16]=t("label",{for:"zenMode",class:"toggle-label"},null,-1))])]),t("div",v1,[e[19]||(e[19]=t("label",null,"打字机模式",-1)),t("div",g1,[u(t("input",{type:"checkbox",id:"typewriterMode","onUpdate:modelValue":e[4]||(e[4]=s=>n.value.typewriterMode=s),onChange:d},null,544),[[p,n.value.typewriterMode]]),e[18]||(e[18]=t("label",{for:"typewriterMode",class:"toggle-label"},null,-1))])]),t("div",p1,[e[21]||(e[21]=t("label",null,"专注模式",-1)),t("div",C1,[u(t("input",{type:"checkbox",id:"focusMode","onUpdate:modelValue":e[5]||(e[5]=s=>n.value.focusMode=s),onChange:d},null,544),[[p,n.value.focusMode]]),e[20]||(e[20]=t("label",{for:"focusMode",class:"toggle-label"},null,-1))])])])])):g("",!0),v.value==="app"?(i(),o("div",m1,[e[31]||(e[31]=t("h2",null,"应用设置",-1)),t("div",b1,[e[27]||(e[27]=t("h3",null,"自动保存",-1)),t("div",f1,[e[25]||(e[25]=t("label",null,"启用自动保存",-1)),t("div",c1,[u(t("input",{type:"checkbox",id:"autoSave","onUpdate:modelValue":e[6]||(e[6]=s=>l.value.autoSave=s),onChange:S},null,544),[[p,l.value.autoSave]]),e[24]||(e[24]=t("label",{for:"autoSave",class:"toggle-label"},null,-1))])]),l.value.autoSave?(i(),o("div",S1,[e[26]||(e[26]=t("label",null,"自动保存间隔",-1)),t("div",w1,[u(t("input",{type:"range",min:"10",max:"300",step:"10","onUpdate:modelValue":e[7]||(e[7]=s=>l.value.autoSaveInterval=s),onInput:_},null,544),[[c,l.value.autoSaveInterval,void 0,{number:!0}]]),t("span",null,r(Math.floor(l.value.autoSaveInterval/1e3))+"秒",1)])])):g("",!0)]),t("div",y1,[e[30]||(e[30]=t("h3",null,"语言",-1)),t("div",M1,[e[29]||(e[29]=t("label",null,"界面语言",-1)),u(t("select",{"onUpdate:modelValue":e[8]||(e[8]=s=>l.value.language=s),onChange:S},e[28]||(e[28]=[t("option",{value:"zh-CN"},"简体中文",-1),t("option",{value:"en-US"},"English",-1)]),544),[[y,l.value.language]])])])])):g("",!0),v.value==="about"?(i(),o("div",k1,[e[34]||(e[34]=t("h2",null,"关于",-1)),t("div",{class:"about-content"},[e[33]||(e[33]=t("div",{class:"app-info"},[t("div",{class:"app-icon"},[t("svg",{width:"64",height:"64",viewBox:"0 0 24 24"},[t("path",{d:"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z",fill:"currentColor"})])]),t("h3",null,"Novel Writer"),t("p",null,"版本 1.0.0"),t("p",null,"一个现代化的网文写作编辑器")],-1)),t("div",{class:"about-actions"},[t("button",{class:"btn btn-primary",onClick:I},e[32]||(e[32]=[t("svg",{width:"16",height:"16",viewBox:"0 0 24 24"},[t("path",{d:"M12,2A10,10 0 0,0 2,12C2,16.42 4.87,20.17 8.84,21.5C9.34,21.58 9.5,21.27 9.5,21C9.5,20.77 9.5,20.14 9.5,19.31C6.73,19.91 6.14,17.97 6.14,17.97C5.68,16.81 5.03,16.5 5.03,16.5C4.12,15.88 5.1,15.9 5.1,15.9C6.1,15.97 6.63,16.93 6.63,16.93C7.5,18.45 8.97,18 9.54,17.76C9.63,17.11 9.89,16.67 10.17,16.42C7.95,16.17 5.62,15.31 5.62,11.5C5.62,10.39 6,9.5 6.65,8.79C6.55,8.54 6.2,7.5 6.75,6.15C6.75,6.15 7.59,5.88 9.5,7.17C10.29,6.95 11.15,6.84 12,6.84C12.85,6.84 13.71,6.95 14.5,7.17C16.41,5.88 17.25,6.15 17.25,6.15C17.8,7.5 17.45,8.54 17.35,8.79C18,9.5 18.38,10.39 18.38,11.5C18.38,15.32 16.04,16.16 13.81,16.41C14.17,16.72 14.5,17.33 14.5,18.26C14.5,19.6 14.5,20.68 14.5,21C14.5,21.27 14.66,21.59 15.17,21.5C19.14,20.16 22,16.42 22,12A10,10 0 0,0 12,2Z",fill:"currentColor"})],-1),m(" GitHub ",-1)])),t("button",{class:"btn btn-secondary",onClick:z}," 重置设置 ")])])])):g("",!0)])])]))}}),H1=$(L1,[["__scopeId","data-v-5d90a29f"]]);export{H1 as default};
