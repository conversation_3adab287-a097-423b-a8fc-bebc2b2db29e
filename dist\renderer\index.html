<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Novel Writer</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Microsoft YaHei', 'SimSun', sans-serif;
      overflow: hidden;
      background: #f5f5f5;
    }
    
    #app {
      height: 100vh;
      width: 100vw;
    }
    
    /* 加载动画 */
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background: #f5f5f5;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #e0e0e0;
      border-top: 4px solid #007acc;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
  <script type="module" crossorigin src="./assets/main-CHDPsXFQ.js"></script>
  <link rel="stylesheet" crossorigin href="./assets/main-Dctxmy53.css">
</head>
<body>
  <div id="app">
    <div class="loading">
      <div class="loading-spinner"></div>
    </div>
  </div>
</body>
</html>
