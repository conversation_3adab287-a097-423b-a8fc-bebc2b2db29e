import { ipcMain, dialog, shell } from 'electron'
import { DataManager } from './dataManager'
import { promises as fs } from 'fs'
import { join, extname } from 'path'

export class IpcManager {
  constructor(private dataManager: DataManager) {}

  registerHandlers(): void {
    // 书籍管理
    ipcMain.handle('book:create', async (_, bookData) => {
      return await this.dataManager.createBook(bookData)
    })

    ipcMain.handle('book:update', async (_, id, updates) => {
      return await this.dataManager.updateBook(id, updates)
    })

    ipcMain.handle('book:delete', async (_, id) => {
      return await this.dataManager.deleteBook(id)
    })

    ipcMain.handle('book:getAll', async () => {
      return await this.dataManager.getAllBooks()
    })

    ipcMain.handle('book:getById', async (_, id) => {
      return await this.dataManager.getBookById(id)
    })

    // 章节管理
    ipcMain.handle('chapter:create', async (_, chapterData) => {
      return await this.dataManager.createChapter(chapterData)
    })

    ipcMain.handle('chapter:update', async (_, id, updates) => {
      return await this.dataManager.updateChapter(id, updates)
    })

    ipcMain.handle('chapter:delete', async (_, id) => {
      return await this.dataManager.deleteChapter(id)
    })

    ipcMain.handle('chapter:getByBookId', async (_, bookId) => {
      return await this.dataManager.getChaptersByBookId(bookId)
    })

    ipcMain.handle('chapter:getById', async (_, id) => {
      return await this.dataManager.getChapterById(id)
    })

    // 设置管理
    ipcMain.handle('settings:get', async () => {
      return await this.dataManager.getSettings()
    })

    ipcMain.handle('settings:update', async (_, settings) => {
      return await this.dataManager.updateSettings(settings)
    })

    // 文件操作
    ipcMain.handle('file:export', async (_, bookId, format) => {
      return await this.exportBook(bookId, format)
    })

    ipcMain.handle('file:import', async (_, filePath) => {
      return await this.importFile(filePath)
    })

    // 窗口控制
    ipcMain.handle('window:minimize', (event) => {
      const window = (event.sender as any).getOwnerBrowserWindow()
      window?.minimize()
    })

    ipcMain.handle('window:maximize', (event) => {
      const window = (event.sender as any).getOwnerBrowserWindow()
      if (window?.isMaximized()) {
        window.unmaximize()
      } else {
        window?.maximize()
      }
    })

    ipcMain.handle('window:close', (event) => {
      const window = (event.sender as any).getOwnerBrowserWindow()
      window?.close()
    })

    ipcMain.handle('window:toggleFullscreen', (event) => {
      const window = (event.sender as any).getOwnerBrowserWindow()
      if (window) {
        const isFullscreen = window.isFullScreen()
        window.setFullScreen(!isFullscreen)
      }
    })

    // 对话框
    ipcMain.handle('dialog:showSaveDialog', async (event, options) => {
      const window = (event.sender as any).getOwnerBrowserWindow()
      if (!window) return { canceled: true }

      return await dialog.showSaveDialog(window, options)
    })

    ipcMain.handle('dialog:showOpenDialog', async (event, options) => {
      const window = (event.sender as any).getOwnerBrowserWindow()
      if (!window) return { canceled: true }

      return await dialog.showOpenDialog(window, options)
    })
  }

  private async exportBook(bookId: string, format: 'txt' | 'docx' | 'pdf'): Promise<string> {
    try {
      const book = await this.dataManager.getBookById(bookId)
      if (!book) {
        throw new Error('书籍不存在')
      }

      const chapters = await this.dataManager.getChaptersByBookId(bookId)
      
      // 生成内容
      let content = `${book.title}\n`
      content += `作者：${book.author}\n\n`
      
      if (book.description) {
        content += `简介：\n${book.description}\n\n`
      }

      content += '='.repeat(50) + '\n\n'

      for (const chapter of chapters) {
        content += `第${chapter.order}章 ${chapter.title}\n\n`
        content += chapter.content + '\n\n'
      }

      // 根据格式处理
      switch (format) {
        case 'txt':
          return await this.exportAsTxt(book.title, content)
        case 'docx':
          return await this.exportAsDocx(book.title, content)
        case 'pdf':
          return await this.exportAsPdf(book.title, content)
        default:
          throw new Error('不支持的导出格式')
      }
    } catch (error: any) {
      throw new Error(`导出失败: ${error.message}`)
    }
  }

  private async exportAsTxt(title: string, content: string): Promise<string> {
    const result = await dialog.showSaveDialog({
      title: '导出为 TXT',
      defaultPath: `${title}.txt`,
      filters: [
        { name: 'Text Files', extensions: ['txt'] }
      ]
    })

    if (result.canceled || !result.filePath) {
      throw new Error('用户取消导出')
    }

    await fs.writeFile(result.filePath, content, 'utf8')
    return result.filePath
  }

  private async exportAsDocx(title: string, content: string): Promise<string> {
    // 这里需要实现 Word 文档导出
    // 可以使用 docx 库或其他方案
    throw new Error('Word 导出功能待实现')
  }

  private async exportAsPdf(title: string, content: string): Promise<string> {
    // 这里需要实现 PDF 导出
    // 可以使用 puppeteer 或其他方案
    throw new Error('PDF 导出功能待实现')
  }

  private async importFile(filePath: string): Promise<{ title: string; content: string }> {
    try {
      const ext = extname(filePath).toLowerCase()
      
      if (ext !== '.txt') {
        throw new Error('目前只支持导入 TXT 文件')
      }

      const content = await fs.readFile(filePath, 'utf8')
      const fileName = join(filePath).split(/[/\\]/).pop() || 'untitled'
      const title = fileName.replace(/\.[^/.]+$/, '')

      return { title, content }
    } catch (error: any) {
      throw new Error(`导入失败: ${error.message}`)
    }
  }
}
