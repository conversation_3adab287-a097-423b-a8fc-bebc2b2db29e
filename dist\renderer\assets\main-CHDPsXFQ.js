const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./Library-OibGlRi8.js","./Library-DOSUrLZa.css","./Editor-sV1BZILL.js","./Editor-DVCYqOxR.css","./Settings-C5O1G57G.js","./Settings-wWDFASoq.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Cs(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ee={},It=[],Ue=()=>{},xi=()=>!1,Mn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Ps=e=>e.startsWith("onUpdate:"),pe=Object.assign,Rs=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Ei=Object.prototype.hasOwnProperty,J=(e,t)=>Ei.call(e,t),$=Array.isArray,Tt=e=>un(e)==="[object Map]",Ht=e=>un(e)==="[object Set]",Zs=e=>un(e)==="[object Date]",K=e=>typeof e=="function",ue=e=>typeof e=="string",We=e=>typeof e=="symbol",se=e=>e!==null&&typeof e=="object",Jr=e=>(se(e)||K(e))&&K(e.then)&&K(e.catch),Zr=Object.prototype.toString,un=e=>Zr.call(e),Ci=e=>un(e).slice(8,-1),Xr=e=>un(e)==="[object Object]",As=e=>ue(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Kt=Cs(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),kn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Pi=/-(\w)/g,Me=kn(e=>e.replace(Pi,(t,n)=>n?n.toUpperCase():"")),Ri=/\B([A-Z])/g,St=kn(e=>e.replace(Ri,"-$1").toLowerCase()),Fn=kn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Gn=kn(e=>e?`on${Fn(e)}`:""),dt=(e,t)=>!Object.is(e,t),vn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},us=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},xn=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Xs;const Ln=()=>Xs||(Xs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Is(e){if($(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=ue(s)?Oi(s):Is(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(ue(e)||se(e))return e}const Ai=/;(?![^(]*\))/g,Ii=/:([^]+)/,Ti=/\/\*[^]*?\*\//g;function Oi(e){const t={};return e.replace(Ti,"").split(Ai).forEach(n=>{if(n){const s=n.split(Ii);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Nn(e){let t="";if(ue(e))t=e;else if($(e))for(let n=0;n<e.length;n++){const s=Nn(e[n]);s&&(t+=s+" ")}else if(se(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Mi="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",ki=Cs(Mi);function eo(e){return!!e||e===""}function Fi(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=fn(e[s],t[s]);return n}function fn(e,t){if(e===t)return!0;let n=Zs(e),s=Zs(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=We(e),s=We(t),n||s)return e===t;if(n=$(e),s=$(t),n||s)return n&&s?Fi(e,t):!1;if(n=se(e),s=se(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,o=Object.keys(t).length;if(r!==o)return!1;for(const i in e){const l=e.hasOwnProperty(i),c=t.hasOwnProperty(i);if(l&&!c||!l&&c||!fn(e[i],t[i]))return!1}}return String(e)===String(t)}function Ts(e,t){return e.findIndex(n=>fn(n,t))}const to=e=>!!(e&&e.__v_isRef===!0),fs=e=>ue(e)?e:e==null?"":$(e)||se(e)&&(e.toString===Zr||!K(e.toString))?to(e)?fs(e.value):JSON.stringify(e,no,2):String(e),no=(e,t)=>to(t)?no(e,t.value):Tt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[Yn(s,o)+" =>"]=r,n),{})}:Ht(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Yn(n))}:We(t)?Yn(t):se(t)&&!$(t)&&!Xr(t)?String(t):t,Yn=(e,t="")=>{var n;return We(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ae;class so{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ae,!t&&ae&&(this.index=(ae.scopes||(ae.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=ae;try{return ae=this,t()}finally{ae=n}}}on(){++this._on===1&&(this.prevScope=ae,ae=this)}off(){this._on>0&&--this._on===0&&(ae=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function ro(e){return new so(e)}function oo(){return ae}function Li(e,t=!1){ae&&ae.cleanups.push(e)}let ne;const Qn=new WeakSet;class io{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ae&&ae.active&&ae.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Qn.has(this)&&(Qn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||co(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,er(this),uo(this);const t=ne,n=Fe;ne=this,Fe=!0;try{return this.fn()}finally{fo(this),ne=t,Fe=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)ks(t);this.deps=this.depsTail=void 0,er(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Qn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){as(this)&&this.run()}get dirty(){return as(this)}}let lo=0,Ut,Wt;function co(e,t=!1){if(e.flags|=8,t){e.next=Wt,Wt=e;return}e.next=Ut,Ut=e}function Os(){lo++}function Ms(){if(--lo>0)return;if(Wt){let t=Wt;for(Wt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Ut;){let t=Ut;for(Ut=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function uo(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function fo(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),ks(s),Ni(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function as(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(ao(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function ao(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===en)||(e.globalVersion=en,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!as(e))))return;e.flags|=2;const t=e.dep,n=ne,s=Fe;ne=e,Fe=!0;try{uo(e);const r=e.fn(e._value);(t.version===0||dt(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{ne=n,Fe=s,fo(e),e.flags&=-3}}function ks(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)ks(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Ni(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Fe=!0;const ho=[];function tt(){ho.push(Fe),Fe=!1}function nt(){const e=ho.pop();Fe=e===void 0?!0:e}function er(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ne;ne=void 0;try{t()}finally{ne=n}}}let en=0;class Hi{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Fs{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!ne||!Fe||ne===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ne)n=this.activeLink=new Hi(ne,this),ne.deps?(n.prevDep=ne.depsTail,ne.depsTail.nextDep=n,ne.depsTail=n):ne.deps=ne.depsTail=n,po(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=ne.depsTail,n.nextDep=void 0,ne.depsTail.nextDep=n,ne.depsTail=n,ne.deps===n&&(ne.deps=s)}return n}trigger(t){this.version++,en++,this.notify(t)}notify(t){Os();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Ms()}}}function po(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)po(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const En=new WeakMap,bt=Symbol(""),ds=Symbol(""),tn=Symbol("");function de(e,t,n){if(Fe&&ne){let s=En.get(e);s||En.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new Fs),r.map=s,r.key=n),r.track()}}function Ze(e,t,n,s,r,o){const i=En.get(e);if(!i){en++;return}const l=c=>{c&&c.trigger()};if(Os(),t==="clear")i.forEach(l);else{const c=$(e),a=c&&As(n);if(c&&n==="length"){const u=Number(s);i.forEach((h,g)=>{(g==="length"||g===tn||!We(g)&&g>=u)&&l(h)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),a&&l(i.get(tn)),t){case"add":c?a&&l(i.get("length")):(l(i.get(bt)),Tt(e)&&l(i.get(ds)));break;case"delete":c||(l(i.get(bt)),Tt(e)&&l(i.get(ds)));break;case"set":Tt(e)&&l(i.get(bt));break}}Ms()}function ji(e,t){const n=En.get(e);return n&&n.get(t)}function Ct(e){const t=G(e);return t===e?t:(de(t,"iterate",tn),Te(e)?t:t.map(fe))}function Hn(e){return de(e=G(e),"iterate",tn),e}const $i={__proto__:null,[Symbol.iterator](){return Jn(this,Symbol.iterator,fe)},concat(...e){return Ct(this).concat(...e.map(t=>$(t)?Ct(t):t))},entries(){return Jn(this,"entries",e=>(e[1]=fe(e[1]),e))},every(e,t){return Ge(this,"every",e,t,void 0,arguments)},filter(e,t){return Ge(this,"filter",e,t,n=>n.map(fe),arguments)},find(e,t){return Ge(this,"find",e,t,fe,arguments)},findIndex(e,t){return Ge(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ge(this,"findLast",e,t,fe,arguments)},findLastIndex(e,t){return Ge(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ge(this,"forEach",e,t,void 0,arguments)},includes(...e){return Zn(this,"includes",e)},indexOf(...e){return Zn(this,"indexOf",e)},join(e){return Ct(this).join(e)},lastIndexOf(...e){return Zn(this,"lastIndexOf",e)},map(e,t){return Ge(this,"map",e,t,void 0,arguments)},pop(){return $t(this,"pop")},push(...e){return $t(this,"push",e)},reduce(e,...t){return tr(this,"reduce",e,t)},reduceRight(e,...t){return tr(this,"reduceRight",e,t)},shift(){return $t(this,"shift")},some(e,t){return Ge(this,"some",e,t,void 0,arguments)},splice(...e){return $t(this,"splice",e)},toReversed(){return Ct(this).toReversed()},toSorted(e){return Ct(this).toSorted(e)},toSpliced(...e){return Ct(this).toSpliced(...e)},unshift(...e){return $t(this,"unshift",e)},values(){return Jn(this,"values",fe)}};function Jn(e,t,n){const s=Hn(e),r=s[t]();return s!==e&&!Te(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const Di=Array.prototype;function Ge(e,t,n,s,r,o){const i=Hn(e),l=i!==e&&!Te(e),c=i[t];if(c!==Di[t]){const h=c.apply(e,o);return l?fe(h):h}let a=n;i!==e&&(l?a=function(h,g){return n.call(this,fe(h),g,e)}:n.length>2&&(a=function(h,g){return n.call(this,h,g,e)}));const u=c.call(i,a,s);return l&&r?r(u):u}function tr(e,t,n,s){const r=Hn(e);let o=n;return r!==e&&(Te(e)?n.length>3&&(o=function(i,l,c){return n.call(this,i,l,c,e)}):o=function(i,l,c){return n.call(this,i,fe(l),c,e)}),r[t](o,...s)}function Zn(e,t,n){const s=G(e);de(s,"iterate",tn);const r=s[t](...n);return(r===-1||r===!1)&&Hs(n[0])?(n[0]=G(n[0]),s[t](...n)):r}function $t(e,t,n=[]){tt(),Os();const s=G(e)[t].apply(e,n);return Ms(),nt(),s}const Bi=Cs("__proto__,__v_isRef,__isVue"),go=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(We));function Vi(e){We(e)||(e=String(e));const t=G(this);return de(t,"has",e),t.hasOwnProperty(e)}class mo{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?Zi:bo:o?_o:vo).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=$(t);if(!r){let c;if(i&&(c=$i[n]))return c;if(n==="hasOwnProperty")return Vi}const l=Reflect.get(t,n,le(t)?t:s);return(We(n)?go.has(n):Bi(n))||(r||de(t,"get",n),o)?l:le(l)?i&&As(n)?l:l.value:se(l)?r?So(l):an(l):l}}class yo extends mo{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const c=pt(o);if(!Te(s)&&!pt(s)&&(o=G(o),s=G(s)),!$(t)&&le(o)&&!le(s))return c?!1:(o.value=s,!0)}const i=$(t)&&As(n)?Number(n)<t.length:J(t,n),l=Reflect.set(t,n,s,le(t)?t:r);return t===G(r)&&(i?dt(s,o)&&Ze(t,"set",n,s):Ze(t,"add",n,s)),l}deleteProperty(t,n){const s=J(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&Ze(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!We(n)||!go.has(n))&&de(t,"has",n),s}ownKeys(t){return de(t,"iterate",$(t)?"length":bt),Reflect.ownKeys(t)}}class Ki extends mo{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Ui=new yo,Wi=new Ki,zi=new yo(!0);const hs=e=>e,gn=e=>Reflect.getPrototypeOf(e);function qi(e,t,n){return function(...s){const r=this.__v_raw,o=G(r),i=Tt(o),l=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,a=r[e](...s),u=n?hs:t?Cn:fe;return!t&&de(o,"iterate",c?ds:bt),{next(){const{value:h,done:g}=a.next();return g?{value:h,done:g}:{value:l?[u(h[0]),u(h[1])]:u(h),done:g}},[Symbol.iterator](){return this}}}}function mn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Gi(e,t){const n={get(r){const o=this.__v_raw,i=G(o),l=G(r);e||(dt(r,l)&&de(i,"get",r),de(i,"get",l));const{has:c}=gn(i),a=t?hs:e?Cn:fe;if(c.call(i,r))return a(o.get(r));if(c.call(i,l))return a(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&de(G(r),"iterate",bt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=G(o),l=G(r);return e||(dt(r,l)&&de(i,"has",r),de(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,c=G(l),a=t?hs:e?Cn:fe;return!e&&de(c,"iterate",bt),l.forEach((u,h)=>r.call(o,a(u),a(h),i))}};return pe(n,e?{add:mn("add"),set:mn("set"),delete:mn("delete"),clear:mn("clear")}:{add(r){!t&&!Te(r)&&!pt(r)&&(r=G(r));const o=G(this);return gn(o).has.call(o,r)||(o.add(r),Ze(o,"add",r,r)),this},set(r,o){!t&&!Te(o)&&!pt(o)&&(o=G(o));const i=G(this),{has:l,get:c}=gn(i);let a=l.call(i,r);a||(r=G(r),a=l.call(i,r));const u=c.call(i,r);return i.set(r,o),a?dt(o,u)&&Ze(i,"set",r,o):Ze(i,"add",r,o),this},delete(r){const o=G(this),{has:i,get:l}=gn(o);let c=i.call(o,r);c||(r=G(r),c=i.call(o,r)),l&&l.call(o,r);const a=o.delete(r);return c&&Ze(o,"delete",r,void 0),a},clear(){const r=G(this),o=r.size!==0,i=r.clear();return o&&Ze(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=qi(r,e,t)}),n}function Ls(e,t){const n=Gi(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(J(n,r)&&r in s?n:s,r,o)}const Yi={get:Ls(!1,!1)},Qi={get:Ls(!1,!0)},Ji={get:Ls(!0,!1)};const vo=new WeakMap,_o=new WeakMap,bo=new WeakMap,Zi=new WeakMap;function Xi(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function el(e){return e.__v_skip||!Object.isExtensible(e)?0:Xi(Ci(e))}function an(e){return pt(e)?e:Ns(e,!1,Ui,Yi,vo)}function wo(e){return Ns(e,!1,zi,Qi,_o)}function So(e){return Ns(e,!0,Wi,Ji,bo)}function Ns(e,t,n,s,r){if(!se(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=el(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?s:n);return r.set(e,l),l}function ht(e){return pt(e)?ht(e.__v_raw):!!(e&&e.__v_isReactive)}function pt(e){return!!(e&&e.__v_isReadonly)}function Te(e){return!!(e&&e.__v_isShallow)}function Hs(e){return e?!!e.__v_raw:!1}function G(e){const t=e&&e.__v_raw;return t?G(t):e}function js(e){return!J(e,"__v_skip")&&Object.isExtensible(e)&&us(e,"__v_skip",!0),e}const fe=e=>se(e)?an(e):e,Cn=e=>se(e)?So(e):e;function le(e){return e?e.__v_isRef===!0:!1}function Ie(e){return xo(e,!1)}function tl(e){return xo(e,!0)}function xo(e,t){return le(e)?e:new nl(e,t)}class nl{constructor(t,n){this.dep=new Fs,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:G(t),this._value=n?t:fe(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Te(t)||pt(t);t=s?t:G(t),dt(t,n)&&(this._rawValue=t,this._value=s?t:fe(t),this.dep.trigger())}}function Ot(e){return le(e)?e.value:e}const sl={get:(e,t,n)=>t==="__v_raw"?e:Ot(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return le(r)&&!le(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function Eo(e){return ht(e)?e:new Proxy(e,sl)}function rl(e){const t=$(e)?new Array(e.length):{};for(const n in e)t[n]=il(e,n);return t}class ol{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return ji(G(this._object),this._key)}}function il(e,t,n){const s=e[t];return le(s)?s:new ol(e,t,n)}class ll{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Fs(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=en-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&ne!==this)return co(this,!0),!0}get value(){const t=this.dep.track();return ao(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function cl(e,t,n=!1){let s,r;return K(e)?s=e:(s=e.get,r=e.set),new ll(s,r,n)}const yn={},Pn=new WeakMap;let _t;function ul(e,t=!1,n=_t){if(n){let s=Pn.get(n);s||Pn.set(n,s=[]),s.push(e)}}function fl(e,t,n=ee){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:l,call:c}=n,a=w=>r?w:Te(w)||r===!1||r===0?Xe(w,1):Xe(w);let u,h,g,m,C=!1,I=!1;if(le(e)?(h=()=>e.value,C=Te(e)):ht(e)?(h=()=>a(e),C=!0):$(e)?(I=!0,C=e.some(w=>ht(w)||Te(w)),h=()=>e.map(w=>{if(le(w))return w.value;if(ht(w))return a(w);if(K(w))return c?c(w,2):w()})):K(e)?t?h=c?()=>c(e,2):e:h=()=>{if(g){tt();try{g()}finally{nt()}}const w=_t;_t=u;try{return c?c(e,3,[m]):e(m)}finally{_t=w}}:h=Ue,t&&r){const w=h,R=r===!0?1/0:r;h=()=>Xe(w(),R)}const B=oo(),N=()=>{u.stop(),B&&B.active&&Rs(B.effects,u)};if(o&&t){const w=t;t=(...R)=>{w(...R),N()}}let F=I?new Array(e.length).fill(yn):yn;const S=w=>{if(!(!(u.flags&1)||!u.dirty&&!w))if(t){const R=u.run();if(r||C||(I?R.some((z,A)=>dt(z,F[A])):dt(R,F))){g&&g();const z=_t;_t=u;try{const A=[R,F===yn?void 0:I&&F[0]===yn?[]:F,m];F=R,c?c(t,3,A):t(...A)}finally{_t=z}}}else u.run()};return l&&l(S),u=new io(h),u.scheduler=i?()=>i(S,!1):S,m=w=>ul(w,!1,u),g=u.onStop=()=>{const w=Pn.get(u);if(w){if(c)c(w,4);else for(const R of w)R();Pn.delete(u)}},t?s?S(!0):F=u.run():i?i(S.bind(null,!0),!0):u.run(),N.pause=u.pause.bind(u),N.resume=u.resume.bind(u),N.stop=N,N}function Xe(e,t=1/0,n){if(t<=0||!se(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,le(e))Xe(e.value,t,n);else if($(e))for(let s=0;s<e.length;s++)Xe(e[s],t,n);else if(Ht(e)||Tt(e))e.forEach(s=>{Xe(s,t,n)});else if(Xr(e)){for(const s in e)Xe(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&Xe(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function dn(e,t,n,s){try{return s?e(...s):e()}catch(r){jn(r,t,n)}}function ze(e,t,n,s){if(K(e)){const r=dn(e,t,n,s);return r&&Jr(r)&&r.catch(o=>{jn(o,t,n)}),r}if($(e)){const r=[];for(let o=0;o<e.length;o++)r.push(ze(e[o],t,n,s));return r}}function jn(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||ee;if(t){let l=t.parent;const c=t.proxy,a=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const u=l.ec;if(u){for(let h=0;h<u.length;h++)if(u[h](e,c,a)===!1)return}l=l.parent}if(o){tt(),dn(o,null,10,[e,c,a]),nt();return}}al(e,n,r,s,i)}function al(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const ye=[];let Be=-1;const Mt=[];let ct=null,Rt=0;const Co=Promise.resolve();let Rn=null;function $n(e){const t=Rn||Co;return e?t.then(this?e.bind(this):e):t}function dl(e){let t=Be+1,n=ye.length;for(;t<n;){const s=t+n>>>1,r=ye[s],o=nn(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function $s(e){if(!(e.flags&1)){const t=nn(e),n=ye[ye.length-1];!n||!(e.flags&2)&&t>=nn(n)?ye.push(e):ye.splice(dl(t),0,e),e.flags|=1,Po()}}function Po(){Rn||(Rn=Co.then(Ao))}function hl(e){$(e)?Mt.push(...e):ct&&e.id===-1?ct.splice(Rt+1,0,e):e.flags&1||(Mt.push(e),e.flags|=1),Po()}function nr(e,t,n=Be+1){for(;n<ye.length;n++){const s=ye[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;ye.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function Ro(e){if(Mt.length){const t=[...new Set(Mt)].sort((n,s)=>nn(n)-nn(s));if(Mt.length=0,ct){ct.push(...t);return}for(ct=t,Rt=0;Rt<ct.length;Rt++){const n=ct[Rt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}ct=null,Rt=0}}const nn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Ao(e){try{for(Be=0;Be<ye.length;Be++){const t=ye[Be];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),dn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Be<ye.length;Be++){const t=ye[Be];t&&(t.flags&=-2)}Be=-1,ye.length=0,Ro(),Rn=null,(ye.length||Mt.length)&&Ao()}}let Pe=null,Io=null;function An(e){const t=Pe;return Pe=e,Io=e&&e.type.__scopeId||null,t}function pl(e,t=Pe,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&dr(-1);const o=An(t);let i;try{i=e(...r)}finally{An(o),s._d&&dr(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function Mf(e,t){if(Pe===null)return e;const n=Un(Pe),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,c=ee]=t[r];o&&(K(o)&&(o={mounted:o,updated:o}),o.deep&&Xe(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:c}))}return e}function yt(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let c=l.dir[s];c&&(tt(),ze(c,n,8,[e.el,l,e,t]),nt())}}const gl=Symbol("_vte"),ml=e=>e.__isTeleport;function Ds(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ds(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Dn(e,t){return K(e)?pe({name:e.name},t,{setup:e}):e}function To(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function zt(e,t,n,s,r=!1){if($(e)){e.forEach((C,I)=>zt(C,t&&($(t)?t[I]:t),n,s,r));return}if(qt(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&zt(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?Un(s.component):s.el,i=r?null:o,{i:l,r:c}=e,a=t&&t.r,u=l.refs===ee?l.refs={}:l.refs,h=l.setupState,g=G(h),m=h===ee?()=>!1:C=>J(g,C);if(a!=null&&a!==c&&(ue(a)?(u[a]=null,m(a)&&(h[a]=null)):le(a)&&(a.value=null)),K(c))dn(c,l,12,[i,u]);else{const C=ue(c),I=le(c);if(C||I){const B=()=>{if(e.f){const N=C?m(c)?h[c]:u[c]:c.value;r?$(N)&&Rs(N,o):$(N)?N.includes(o)||N.push(o):C?(u[c]=[o],m(c)&&(h[c]=u[c])):(c.value=[o],e.k&&(u[e.k]=c.value))}else C?(u[c]=i,m(c)&&(h[c]=i)):I&&(c.value=i,e.k&&(u[e.k]=i))};i?(B.id=-1,Ce(B,n)):B()}}}Ln().requestIdleCallback;Ln().cancelIdleCallback;const qt=e=>!!e.type.__asyncLoader,Oo=e=>e.type.__isKeepAlive;function yl(e,t){Mo(e,"a",t)}function vl(e,t){Mo(e,"da",t)}function Mo(e,t,n=he){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Bn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)Oo(r.parent.vnode)&&_l(s,t,n,r),r=r.parent}}function _l(e,t,n,s){const r=Bn(t,e,s,!0);Vs(()=>{Rs(s[t],r)},n)}function Bn(e,t,n=he,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{tt();const l=hn(n),c=ze(t,n,e,i);return l(),nt(),c});return s?r.unshift(o):r.push(o),o}}const st=e=>(t,n=he)=>{(!rn||e==="sp")&&Bn(e,(...s)=>t(...s),n)},bl=st("bm"),Bs=st("m"),wl=st("bu"),Sl=st("u"),xl=st("bum"),Vs=st("um"),El=st("sp"),Cl=st("rtg"),Pl=st("rtc");function Rl(e,t=he){Bn("ec",e,t)}const Al="components";function Il(e,t){return Ol(Al,e,!0,t)||e}const Tl=Symbol.for("v-ndc");function Ol(e,t,n=!0,s=!1){const r=Pe||he;if(r){const o=r.type;{const l=_c(o,!1);if(l&&(l===t||l===Me(t)||l===Fn(Me(t))))return o}const i=sr(r[e]||o[e],t)||sr(r.appContext[e],t);return!i&&s?o:i}}function sr(e,t){return e&&(e[t]||e[Me(t)]||e[Fn(Me(t))])}function Ml(e,t,n,s){let r;const o=n,i=$(e);if(i||ue(e)){const l=i&&ht(e);let c=!1,a=!1;l&&(c=!Te(e),a=pt(e),e=Hn(e)),r=new Array(e.length);for(let u=0,h=e.length;u<h;u++)r[u]=t(c?a?Cn(fe(e[u])):fe(e[u]):e[u],u,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,o)}else if(se(e))if(e[Symbol.iterator])r=Array.from(e,(l,c)=>t(l,c,void 0,o));else{const l=Object.keys(e);r=new Array(l.length);for(let c=0,a=l.length;c<a;c++){const u=l[c];r[c]=t(e[u],u,c,o)}}else r=[];return r}const ps=e=>e?ti(e)?Un(e):ps(e.parent):null,Gt=pe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ps(e.parent),$root:e=>ps(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Fo(e),$forceUpdate:e=>e.f||(e.f=()=>{$s(e.update)}),$nextTick:e=>e.n||(e.n=$n.bind(e.proxy)),$watch:e=>ec.bind(e)}),Xn=(e,t)=>e!==ee&&!e.__isScriptSetup&&J(e,t),kl={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:l,appContext:c}=e;let a;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(Xn(s,t))return i[t]=1,s[t];if(r!==ee&&J(r,t))return i[t]=2,r[t];if((a=e.propsOptions[0])&&J(a,t))return i[t]=3,o[t];if(n!==ee&&J(n,t))return i[t]=4,n[t];gs&&(i[t]=0)}}const u=Gt[t];let h,g;if(u)return t==="$attrs"&&de(e.attrs,"get",""),u(e);if((h=l.__cssModules)&&(h=h[t]))return h;if(n!==ee&&J(n,t))return i[t]=4,n[t];if(g=c.config.globalProperties,J(g,t))return g[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return Xn(r,t)?(r[t]=n,!0):s!==ee&&J(s,t)?(s[t]=n,!0):J(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let l;return!!n[i]||e!==ee&&J(e,i)||Xn(t,i)||(l=o[0])&&J(l,i)||J(s,i)||J(Gt,i)||J(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:J(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function rr(e){return $(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let gs=!0;function Fl(e){const t=Fo(e),n=e.proxy,s=e.ctx;gs=!1,t.beforeCreate&&or(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:c,inject:a,created:u,beforeMount:h,mounted:g,beforeUpdate:m,updated:C,activated:I,deactivated:B,beforeDestroy:N,beforeUnmount:F,destroyed:S,unmounted:w,render:R,renderTracked:z,renderTriggered:A,errorCaptured:D,serverPrefetch:W,expose:oe,inheritAttrs:ge,components:xe,directives:ve,filters:mt}=t;if(a&&Ll(a,s,null),i)for(const U in i){const Y=i[U];K(Y)&&(s[U]=Y.bind(n))}if(r){const U=r.call(n,n);se(U)&&(e.data=an(U))}if(gs=!0,o)for(const U in o){const Y=o[U],qe=K(Y)?Y.bind(n,n):K(Y.get)?Y.get.bind(n,n):Ue,ot=!K(Y)&&K(Y.set)?Y.set.bind(n):Ue,Ne=ce({get:qe,set:ot});Object.defineProperty(s,U,{enumerable:!0,configurable:!0,get:()=>Ne.value,set:_e=>Ne.value=_e})}if(l)for(const U in l)ko(l[U],s,n,U);if(c){const U=K(c)?c.call(n):c;Reflect.ownKeys(U).forEach(Y=>{_n(Y,U[Y])})}u&&or(u,e,"c");function re(U,Y){$(Y)?Y.forEach(qe=>U(qe.bind(n))):Y&&U(Y.bind(n))}if(re(bl,h),re(Bs,g),re(wl,m),re(Sl,C),re(yl,I),re(vl,B),re(Rl,D),re(Pl,z),re(Cl,A),re(xl,F),re(Vs,w),re(El,W),$(oe))if(oe.length){const U=e.exposed||(e.exposed={});oe.forEach(Y=>{Object.defineProperty(U,Y,{get:()=>n[Y],set:qe=>n[Y]=qe,enumerable:!0})})}else e.exposed||(e.exposed={});R&&e.render===Ue&&(e.render=R),ge!=null&&(e.inheritAttrs=ge),xe&&(e.components=xe),ve&&(e.directives=ve),W&&To(e)}function Ll(e,t,n=Ue){$(e)&&(e=ms(e));for(const s in e){const r=e[s];let o;se(r)?"default"in r?o=Oe(r.from||s,r.default,!0):o=Oe(r.from||s):o=Oe(r),le(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function or(e,t,n){ze($(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function ko(e,t,n,s){let r=s.includes(".")?qo(n,s):()=>n[s];if(ue(e)){const o=t[e];K(o)&&Yt(r,o)}else if(K(e))Yt(r,e.bind(n));else if(se(e))if($(e))e.forEach(o=>ko(o,t,n,s));else{const o=K(e.handler)?e.handler.bind(n):t[e.handler];K(o)&&Yt(r,o,e)}}function Fo(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let c;return l?c=l:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(a=>In(c,a,i,!0)),In(c,t,i)),se(t)&&o.set(t,c),c}function In(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&In(e,o,n,!0),r&&r.forEach(i=>In(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=Nl[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const Nl={data:ir,props:lr,emits:lr,methods:Vt,computed:Vt,beforeCreate:me,created:me,beforeMount:me,mounted:me,beforeUpdate:me,updated:me,beforeDestroy:me,beforeUnmount:me,destroyed:me,unmounted:me,activated:me,deactivated:me,errorCaptured:me,serverPrefetch:me,components:Vt,directives:Vt,watch:jl,provide:ir,inject:Hl};function ir(e,t){return t?e?function(){return pe(K(e)?e.call(this,this):e,K(t)?t.call(this,this):t)}:t:e}function Hl(e,t){return Vt(ms(e),ms(t))}function ms(e){if($(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function me(e,t){return e?[...new Set([].concat(e,t))]:t}function Vt(e,t){return e?pe(Object.create(null),e,t):t}function lr(e,t){return e?$(e)&&$(t)?[...new Set([...e,...t])]:pe(Object.create(null),rr(e),rr(t??{})):t}function jl(e,t){if(!e)return t;if(!t)return e;const n=pe(Object.create(null),e);for(const s in t)n[s]=me(e[s],t[s]);return n}function Lo(){return{app:null,config:{isNativeTag:xi,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let $l=0;function Dl(e,t){return function(s,r=null){K(s)||(s=pe({},s)),r!=null&&!se(r)&&(r=null);const o=Lo(),i=new WeakSet,l=[];let c=!1;const a=o.app={_uid:$l++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:wc,get config(){return o.config},set config(u){},use(u,...h){return i.has(u)||(u&&K(u.install)?(i.add(u),u.install(a,...h)):K(u)&&(i.add(u),u(a,...h))),a},mixin(u){return o.mixins.includes(u)||o.mixins.push(u),a},component(u,h){return h?(o.components[u]=h,a):o.components[u]},directive(u,h){return h?(o.directives[u]=h,a):o.directives[u]},mount(u,h,g){if(!c){const m=a._ceVNode||Se(s,r);return m.appContext=o,g===!0?g="svg":g===!1&&(g=void 0),e(m,u,g),c=!0,a._container=u,u.__vue_app__=a,Un(m.component)}},onUnmount(u){l.push(u)},unmount(){c&&(ze(l,a._instance,16),e(null,a._container),delete a._container.__vue_app__)},provide(u,h){return o.provides[u]=h,a},runWithContext(u){const h=wt;wt=a;try{return u()}finally{wt=h}}};return a}}let wt=null;function _n(e,t){if(he){let n=he.provides;const s=he.parent&&he.parent.provides;s===n&&(n=he.provides=Object.create(s)),n[e]=t}}function Oe(e,t,n=!1){const s=ei();if(s||wt){let r=wt?wt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&K(t)?t.call(s&&s.proxy):t}}function Bl(){return!!(ei()||wt)}const No={},Ho=()=>Object.create(No),jo=e=>Object.getPrototypeOf(e)===No;function Vl(e,t,n,s=!1){const r={},o=Ho();e.propsDefaults=Object.create(null),$o(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:wo(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Kl(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=G(r),[c]=e.propsOptions;let a=!1;if((s||i>0)&&!(i&16)){if(i&8){const u=e.vnode.dynamicProps;for(let h=0;h<u.length;h++){let g=u[h];if(Vn(e.emitsOptions,g))continue;const m=t[g];if(c)if(J(o,g))m!==o[g]&&(o[g]=m,a=!0);else{const C=Me(g);r[C]=ys(c,l,C,m,e,!1)}else m!==o[g]&&(o[g]=m,a=!0)}}}else{$o(e,t,r,o)&&(a=!0);let u;for(const h in l)(!t||!J(t,h)&&((u=St(h))===h||!J(t,u)))&&(c?n&&(n[h]!==void 0||n[u]!==void 0)&&(r[h]=ys(c,l,h,void 0,e,!0)):delete r[h]);if(o!==l)for(const h in o)(!t||!J(t,h))&&(delete o[h],a=!0)}a&&Ze(e.attrs,"set","")}function $o(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let c in t){if(Kt(c))continue;const a=t[c];let u;r&&J(r,u=Me(c))?!o||!o.includes(u)?n[u]=a:(l||(l={}))[u]=a:Vn(e.emitsOptions,c)||(!(c in s)||a!==s[c])&&(s[c]=a,i=!0)}if(o){const c=G(n),a=l||ee;for(let u=0;u<o.length;u++){const h=o[u];n[h]=ys(r,c,h,a[h],e,!J(a,h))}}return i}function ys(e,t,n,s,r,o){const i=e[n];if(i!=null){const l=J(i,"default");if(l&&s===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&K(c)){const{propsDefaults:a}=r;if(n in a)s=a[n];else{const u=hn(r);s=a[n]=c.call(null,t),u()}}else s=c;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!l?s=!1:i[1]&&(s===""||s===St(n))&&(s=!0))}return s}const Ul=new WeakMap;function Do(e,t,n=!1){const s=n?Ul:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},l=[];let c=!1;if(!K(e)){const u=h=>{c=!0;const[g,m]=Do(h,t,!0);pe(i,g),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!o&&!c)return se(e)&&s.set(e,It),It;if($(o))for(let u=0;u<o.length;u++){const h=Me(o[u]);cr(h)&&(i[h]=ee)}else if(o)for(const u in o){const h=Me(u);if(cr(h)){const g=o[u],m=i[h]=$(g)||K(g)?{type:g}:pe({},g),C=m.type;let I=!1,B=!0;if($(C))for(let N=0;N<C.length;++N){const F=C[N],S=K(F)&&F.name;if(S==="Boolean"){I=!0;break}else S==="String"&&(B=!1)}else I=K(C)&&C.name==="Boolean";m[0]=I,m[1]=B,(I||J(m,"default"))&&l.push(h)}}const a=[i,l];return se(e)&&s.set(e,a),a}function cr(e){return e[0]!=="$"&&!Kt(e)}const Ks=e=>e==="_"||e==="__"||e==="_ctx"||e==="$stable",Us=e=>$(e)?e.map(Ke):[Ke(e)],Wl=(e,t,n)=>{if(t._n)return t;const s=pl((...r)=>Us(t(...r)),n);return s._c=!1,s},Bo=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Ks(r))continue;const o=e[r];if(K(o))t[r]=Wl(r,o,s);else if(o!=null){const i=Us(o);t[r]=()=>i}}},Vo=(e,t)=>{const n=Us(t);e.slots.default=()=>n},Ko=(e,t,n)=>{for(const s in t)(n||!Ks(s))&&(e[s]=t[s])},zl=(e,t,n)=>{const s=e.slots=Ho();if(e.vnode.shapeFlag&32){const r=t.__;r&&us(s,"__",r,!0);const o=t._;o?(Ko(s,t,n),n&&us(s,"_",o,!0)):Bo(t,s)}else t&&Vo(e,t)},ql=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=ee;if(s.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:Ko(r,t,n):(o=!t.$stable,Bo(t,r)),i=t}else t&&(Vo(e,t),i={default:1});if(o)for(const l in r)!Ks(l)&&i[l]==null&&delete r[l]},Ce=lc;function Gl(e){return Yl(e)}function Yl(e,t){const n=Ln();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:l,createComment:c,setText:a,setElementText:u,parentNode:h,nextSibling:g,setScopeId:m=Ue,insertStaticContent:C}=e,I=(f,d,p,y=null,b=null,_=null,T=void 0,P=null,E=!!d.dynamicChildren)=>{if(f===d)return;f&&!Dt(f,d)&&(y=v(f),_e(f,b,_,!0),f=null),d.patchFlag===-2&&(E=!1,d.dynamicChildren=null);const{type:x,ref:j,shapeFlag:M}=d;switch(x){case Kn:B(f,d,p,y);break;case gt:N(f,d,p,y);break;case ts:f==null&&F(d,p,y,T);break;case Ve:xe(f,d,p,y,b,_,T,P,E);break;default:M&1?R(f,d,p,y,b,_,T,P,E):M&6?ve(f,d,p,y,b,_,T,P,E):(M&64||M&128)&&x.process(f,d,p,y,b,_,T,P,E,L)}j!=null&&b?zt(j,f&&f.ref,_,d||f,!d):j==null&&f&&f.ref!=null&&zt(f.ref,null,_,f,!0)},B=(f,d,p,y)=>{if(f==null)s(d.el=l(d.children),p,y);else{const b=d.el=f.el;d.children!==f.children&&a(b,d.children)}},N=(f,d,p,y)=>{f==null?s(d.el=c(d.children||""),p,y):d.el=f.el},F=(f,d,p,y)=>{[f.el,f.anchor]=C(f.children,d,p,y,f.el,f.anchor)},S=({el:f,anchor:d},p,y)=>{let b;for(;f&&f!==d;)b=g(f),s(f,p,y),f=b;s(d,p,y)},w=({el:f,anchor:d})=>{let p;for(;f&&f!==d;)p=g(f),r(f),f=p;r(d)},R=(f,d,p,y,b,_,T,P,E)=>{d.type==="svg"?T="svg":d.type==="math"&&(T="mathml"),f==null?z(d,p,y,b,_,T,P,E):W(f,d,b,_,T,P,E)},z=(f,d,p,y,b,_,T,P)=>{let E,x;const{props:j,shapeFlag:M,transition:H,dirs:V}=f;if(E=f.el=i(f.type,_,j&&j.is,j),M&8?u(E,f.children):M&16&&D(f.children,E,null,y,b,es(f,_),T,P),V&&yt(f,null,y,"created"),A(E,f,f.scopeId,T,y),j){for(const te in j)te!=="value"&&!Kt(te)&&o(E,te,null,j[te],_,y);"value"in j&&o(E,"value",null,j.value,_),(x=j.onVnodeBeforeMount)&&De(x,y,f)}V&&yt(f,null,y,"beforeMount");const q=Ql(b,H);q&&H.beforeEnter(E),s(E,d,p),((x=j&&j.onVnodeMounted)||q||V)&&Ce(()=>{x&&De(x,y,f),q&&H.enter(E),V&&yt(f,null,y,"mounted")},b)},A=(f,d,p,y,b)=>{if(p&&m(f,p),y)for(let _=0;_<y.length;_++)m(f,y[_]);if(b){let _=b.subTree;if(d===_||Yo(_.type)&&(_.ssContent===d||_.ssFallback===d)){const T=b.vnode;A(f,T,T.scopeId,T.slotScopeIds,b.parent)}}},D=(f,d,p,y,b,_,T,P,E=0)=>{for(let x=E;x<f.length;x++){const j=f[x]=P?ut(f[x]):Ke(f[x]);I(null,j,d,p,y,b,_,T,P)}},W=(f,d,p,y,b,_,T)=>{const P=d.el=f.el;let{patchFlag:E,dynamicChildren:x,dirs:j}=d;E|=f.patchFlag&16;const M=f.props||ee,H=d.props||ee;let V;if(p&&vt(p,!1),(V=H.onVnodeBeforeUpdate)&&De(V,p,d,f),j&&yt(d,f,p,"beforeUpdate"),p&&vt(p,!0),(M.innerHTML&&H.innerHTML==null||M.textContent&&H.textContent==null)&&u(P,""),x?oe(f.dynamicChildren,x,P,p,y,es(d,b),_):T||Y(f,d,P,null,p,y,es(d,b),_,!1),E>0){if(E&16)ge(P,M,H,p,b);else if(E&2&&M.class!==H.class&&o(P,"class",null,H.class,b),E&4&&o(P,"style",M.style,H.style,b),E&8){const q=d.dynamicProps;for(let te=0;te<q.length;te++){const Z=q[te],be=M[Z],we=H[Z];(we!==be||Z==="value")&&o(P,Z,be,we,b,p)}}E&1&&f.children!==d.children&&u(P,d.children)}else!T&&x==null&&ge(P,M,H,p,b);((V=H.onVnodeUpdated)||j)&&Ce(()=>{V&&De(V,p,d,f),j&&yt(d,f,p,"updated")},y)},oe=(f,d,p,y,b,_,T)=>{for(let P=0;P<d.length;P++){const E=f[P],x=d[P],j=E.el&&(E.type===Ve||!Dt(E,x)||E.shapeFlag&198)?h(E.el):p;I(E,x,j,null,y,b,_,T,!0)}},ge=(f,d,p,y,b)=>{if(d!==p){if(d!==ee)for(const _ in d)!Kt(_)&&!(_ in p)&&o(f,_,d[_],null,b,y);for(const _ in p){if(Kt(_))continue;const T=p[_],P=d[_];T!==P&&_!=="value"&&o(f,_,P,T,b,y)}"value"in p&&o(f,"value",d.value,p.value,b)}},xe=(f,d,p,y,b,_,T,P,E)=>{const x=d.el=f?f.el:l(""),j=d.anchor=f?f.anchor:l("");let{patchFlag:M,dynamicChildren:H,slotScopeIds:V}=d;V&&(P=P?P.concat(V):V),f==null?(s(x,p,y),s(j,p,y),D(d.children||[],p,j,b,_,T,P,E)):M>0&&M&64&&H&&f.dynamicChildren?(oe(f.dynamicChildren,H,p,b,_,T,P),(d.key!=null||b&&d===b.subTree)&&Uo(f,d,!0)):Y(f,d,p,j,b,_,T,P,E)},ve=(f,d,p,y,b,_,T,P,E)=>{d.slotScopeIds=P,f==null?d.shapeFlag&512?b.ctx.activate(d,p,y,T,E):mt(d,p,y,b,_,T,E):rt(f,d,E)},mt=(f,d,p,y,b,_,T)=>{const P=f.component=pc(f,y,b);if(Oo(f)&&(P.ctx.renderer=L),gc(P,!1,T),P.asyncDep){if(b&&b.registerDep(P,re,T),!f.el){const E=P.subTree=Se(gt);N(null,E,d,p),f.placeholder=E.el}}else re(P,f,d,p,b,_,T)},rt=(f,d,p)=>{const y=d.component=f.component;if(oc(f,d,p))if(y.asyncDep&&!y.asyncResolved){U(y,d,p);return}else y.next=d,y.update();else d.el=f.el,y.vnode=d},re=(f,d,p,y,b,_,T)=>{const P=()=>{if(f.isMounted){let{next:M,bu:H,u:V,parent:q,vnode:te}=f;{const je=Wo(f);if(je){M&&(M.el=te.el,U(f,M,T)),je.asyncDep.then(()=>{f.isUnmounted||P()});return}}let Z=M,be;vt(f,!1),M?(M.el=te.el,U(f,M,T)):M=te,H&&vn(H),(be=M.props&&M.props.onVnodeBeforeUpdate)&&De(be,q,M,te),vt(f,!0);const we=fr(f),He=f.subTree;f.subTree=we,I(He,we,h(He.el),v(He),f,b,_),M.el=we.el,Z===null&&ic(f,we.el),V&&Ce(V,b),(be=M.props&&M.props.onVnodeUpdated)&&Ce(()=>De(be,q,M,te),b)}else{let M;const{el:H,props:V}=d,{bm:q,m:te,parent:Z,root:be,type:we}=f,He=qt(d);vt(f,!1),q&&vn(q),!He&&(M=V&&V.onVnodeBeforeMount)&&De(M,Z,d),vt(f,!0);{be.ce&&be.ce._def.shadowRoot!==!1&&be.ce._injectChildStyle(we);const je=f.subTree=fr(f);I(null,je,p,y,f,b,_),d.el=je.el}if(te&&Ce(te,b),!He&&(M=V&&V.onVnodeMounted)){const je=d;Ce(()=>De(M,Z,je),b)}(d.shapeFlag&256||Z&&qt(Z.vnode)&&Z.vnode.shapeFlag&256)&&f.a&&Ce(f.a,b),f.isMounted=!0,d=p=y=null}};f.scope.on();const E=f.effect=new io(P);f.scope.off();const x=f.update=E.run.bind(E),j=f.job=E.runIfDirty.bind(E);j.i=f,j.id=f.uid,E.scheduler=()=>$s(j),vt(f,!0),x()},U=(f,d,p)=>{d.component=f;const y=f.vnode.props;f.vnode=d,f.next=null,Kl(f,d.props,y,p),ql(f,d.children,p),tt(),nr(f),nt()},Y=(f,d,p,y,b,_,T,P,E=!1)=>{const x=f&&f.children,j=f?f.shapeFlag:0,M=d.children,{patchFlag:H,shapeFlag:V}=d;if(H>0){if(H&128){ot(x,M,p,y,b,_,T,P,E);return}else if(H&256){qe(x,M,p,y,b,_,T,P,E);return}}V&8?(j&16&&Ae(x,b,_),M!==x&&u(p,M)):j&16?V&16?ot(x,M,p,y,b,_,T,P,E):Ae(x,b,_,!0):(j&8&&u(p,""),V&16&&D(M,p,y,b,_,T,P,E))},qe=(f,d,p,y,b,_,T,P,E)=>{f=f||It,d=d||It;const x=f.length,j=d.length,M=Math.min(x,j);let H;for(H=0;H<M;H++){const V=d[H]=E?ut(d[H]):Ke(d[H]);I(f[H],V,p,null,b,_,T,P,E)}x>j?Ae(f,b,_,!0,!1,M):D(d,p,y,b,_,T,P,E,M)},ot=(f,d,p,y,b,_,T,P,E)=>{let x=0;const j=d.length;let M=f.length-1,H=j-1;for(;x<=M&&x<=H;){const V=f[x],q=d[x]=E?ut(d[x]):Ke(d[x]);if(Dt(V,q))I(V,q,p,null,b,_,T,P,E);else break;x++}for(;x<=M&&x<=H;){const V=f[M],q=d[H]=E?ut(d[H]):Ke(d[H]);if(Dt(V,q))I(V,q,p,null,b,_,T,P,E);else break;M--,H--}if(x>M){if(x<=H){const V=H+1,q=V<j?d[V].el:y;for(;x<=H;)I(null,d[x]=E?ut(d[x]):Ke(d[x]),p,q,b,_,T,P,E),x++}}else if(x>H)for(;x<=M;)_e(f[x],b,_,!0),x++;else{const V=x,q=x,te=new Map;for(x=q;x<=H;x++){const Ee=d[x]=E?ut(d[x]):Ke(d[x]);Ee.key!=null&&te.set(Ee.key,x)}let Z,be=0;const we=H-q+1;let He=!1,je=0;const jt=new Array(we);for(x=0;x<we;x++)jt[x]=0;for(x=V;x<=M;x++){const Ee=f[x];if(be>=we){_e(Ee,b,_,!0);continue}let $e;if(Ee.key!=null)$e=te.get(Ee.key);else for(Z=q;Z<=H;Z++)if(jt[Z-q]===0&&Dt(Ee,d[Z])){$e=Z;break}$e===void 0?_e(Ee,b,_,!0):(jt[$e-q]=x+1,$e>=je?je=$e:He=!0,I(Ee,d[$e],p,null,b,_,T,P,E),be++)}const Ys=He?Jl(jt):It;for(Z=Ys.length-1,x=we-1;x>=0;x--){const Ee=q+x,$e=d[Ee],Qs=d[Ee+1],Js=Ee+1<j?Qs.el||Qs.placeholder:y;jt[x]===0?I(null,$e,p,Js,b,_,T,P,E):He&&(Z<0||x!==Ys[Z]?Ne($e,p,Js,2):Z--)}}},Ne=(f,d,p,y,b=null)=>{const{el:_,type:T,transition:P,children:E,shapeFlag:x}=f;if(x&6){Ne(f.component.subTree,d,p,y);return}if(x&128){f.suspense.move(d,p,y);return}if(x&64){T.move(f,d,p,L);return}if(T===Ve){s(_,d,p);for(let M=0;M<E.length;M++)Ne(E[M],d,p,y);s(f.anchor,d,p);return}if(T===ts){S(f,d,p);return}if(y!==2&&x&1&&P)if(y===0)P.beforeEnter(_),s(_,d,p),Ce(()=>P.enter(_),b);else{const{leave:M,delayLeave:H,afterLeave:V}=P,q=()=>{f.ctx.isUnmounted?r(_):s(_,d,p)},te=()=>{M(_,()=>{q(),V&&V()})};H?H(_,q,te):te()}else s(_,d,p)},_e=(f,d,p,y=!1,b=!1)=>{const{type:_,props:T,ref:P,children:E,dynamicChildren:x,shapeFlag:j,patchFlag:M,dirs:H,cacheIndex:V}=f;if(M===-2&&(b=!1),P!=null&&(tt(),zt(P,null,p,f,!0),nt()),V!=null&&(d.renderCache[V]=void 0),j&256){d.ctx.deactivate(f);return}const q=j&1&&H,te=!qt(f);let Z;if(te&&(Z=T&&T.onVnodeBeforeUnmount)&&De(Z,d,f),j&6)pn(f.component,p,y);else{if(j&128){f.suspense.unmount(p,y);return}q&&yt(f,null,d,"beforeUnmount"),j&64?f.type.remove(f,d,p,L,y):x&&!x.hasOnce&&(_!==Ve||M>0&&M&64)?Ae(x,d,p,!1,!0):(_===Ve&&M&384||!b&&j&16)&&Ae(E,d,p),y&&xt(f)}(te&&(Z=T&&T.onVnodeUnmounted)||q)&&Ce(()=>{Z&&De(Z,d,f),q&&yt(f,null,d,"unmounted")},p)},xt=f=>{const{type:d,el:p,anchor:y,transition:b}=f;if(d===Ve){Et(p,y);return}if(d===ts){w(f);return}const _=()=>{r(p),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(f.shapeFlag&1&&b&&!b.persisted){const{leave:T,delayLeave:P}=b,E=()=>T(p,_);P?P(f.el,_,E):E()}else _()},Et=(f,d)=>{let p;for(;f!==d;)p=g(f),r(f),f=p;r(d)},pn=(f,d,p)=>{const{bum:y,scope:b,job:_,subTree:T,um:P,m:E,a:x,parent:j,slots:{__:M}}=f;ur(E),ur(x),y&&vn(y),j&&$(M)&&M.forEach(H=>{j.renderCache[H]=void 0}),b.stop(),_&&(_.flags|=8,_e(T,f,d,p)),P&&Ce(P,d),Ce(()=>{f.isUnmounted=!0},d),d&&d.pendingBranch&&!d.isUnmounted&&f.asyncDep&&!f.asyncResolved&&f.suspenseId===d.pendingId&&(d.deps--,d.deps===0&&d.resolve())},Ae=(f,d,p,y=!1,b=!1,_=0)=>{for(let T=_;T<f.length;T++)_e(f[T],d,p,y,b)},v=f=>{if(f.shapeFlag&6)return v(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const d=g(f.anchor||f.el),p=d&&d[gl];return p?g(p):d};let k=!1;const O=(f,d,p)=>{f==null?d._vnode&&_e(d._vnode,null,null,!0):I(d._vnode||null,f,d,null,null,null,p),d._vnode=f,k||(k=!0,nr(),Ro(),k=!1)},L={p:I,um:_e,m:Ne,r:xt,mt,mc:D,pc:Y,pbc:oe,n:v,o:e};return{render:O,hydrate:void 0,createApp:Dl(O)}}function es({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function vt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Ql(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Uo(e,t,n=!1){const s=e.children,r=t.children;if($(s)&&$(r))for(let o=0;o<s.length;o++){const i=s[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=ut(r[o]),l.el=i.el),!n&&l.patchFlag!==-2&&Uo(i,l)),l.type===Kn&&(l.el=i.el),l.type===gt&&!l.el&&(l.el=i.el)}}function Jl(e){const t=e.slice(),n=[0];let s,r,o,i,l;const c=e.length;for(s=0;s<c;s++){const a=e[s];if(a!==0){if(r=n[n.length-1],e[r]<a){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<a?o=l+1:i=l;a<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Wo(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Wo(t)}function ur(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Zl=Symbol.for("v-scx"),Xl=()=>Oe(Zl);function Yt(e,t,n){return zo(e,t,n)}function zo(e,t,n=ee){const{immediate:s,deep:r,flush:o,once:i}=n,l=pe({},n),c=t&&s||!t&&o!=="post";let a;if(rn){if(o==="sync"){const m=Xl();a=m.__watcherHandles||(m.__watcherHandles=[])}else if(!c){const m=()=>{};return m.stop=Ue,m.resume=Ue,m.pause=Ue,m}}const u=he;l.call=(m,C,I)=>ze(m,u,C,I);let h=!1;o==="post"?l.scheduler=m=>{Ce(m,u&&u.suspense)}:o!=="sync"&&(h=!0,l.scheduler=(m,C)=>{C?m():$s(m)}),l.augmentJob=m=>{t&&(m.flags|=4),h&&(m.flags|=2,u&&(m.id=u.uid,m.i=u))};const g=fl(e,t,l);return rn&&(a?a.push(g):c&&g()),g}function ec(e,t,n){const s=this.proxy,r=ue(e)?e.includes(".")?qo(s,e):()=>s[e]:e.bind(s,s);let o;K(t)?o=t:(o=t.handler,n=t);const i=hn(this),l=zo(r,o.bind(s),n);return i(),l}function qo(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const tc=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Me(t)}Modifiers`]||e[`${St(t)}Modifiers`];function nc(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||ee;let r=n;const o=t.startsWith("update:"),i=o&&tc(s,t.slice(7));i&&(i.trim&&(r=n.map(u=>ue(u)?u.trim():u)),i.number&&(r=n.map(xn)));let l,c=s[l=Gn(t)]||s[l=Gn(Me(t))];!c&&o&&(c=s[l=Gn(St(t))]),c&&ze(c,e,6,r);const a=s[l+"Once"];if(a){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,ze(a,e,6,r)}}function Go(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!K(e)){const c=a=>{const u=Go(a,t,!0);u&&(l=!0,pe(i,u))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!o&&!l?(se(e)&&s.set(e,null),null):($(o)?o.forEach(c=>i[c]=null):pe(i,o),se(e)&&s.set(e,i),i)}function Vn(e,t){return!e||!Mn(t)?!1:(t=t.slice(2).replace(/Once$/,""),J(e,t[0].toLowerCase()+t.slice(1))||J(e,St(t))||J(e,t))}function fr(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:c,render:a,renderCache:u,props:h,data:g,setupState:m,ctx:C,inheritAttrs:I}=e,B=An(e);let N,F;try{if(n.shapeFlag&4){const w=r||s,R=w;N=Ke(a.call(R,w,u,h,m,g,C)),F=l}else{const w=t;N=Ke(w.length>1?w(h,{attrs:l,slots:i,emit:c}):w(h,null)),F=t.props?l:sc(l)}}catch(w){Qt.length=0,jn(w,e,1),N=Se(gt)}let S=N;if(F&&I!==!1){const w=Object.keys(F),{shapeFlag:R}=S;w.length&&R&7&&(o&&w.some(Ps)&&(F=rc(F,o)),S=kt(S,F,!1,!0))}return n.dirs&&(S=kt(S,null,!1,!0),S.dirs=S.dirs?S.dirs.concat(n.dirs):n.dirs),n.transition&&Ds(S,n.transition),N=S,An(B),N}const sc=e=>{let t;for(const n in e)(n==="class"||n==="style"||Mn(n))&&((t||(t={}))[n]=e[n]);return t},rc=(e,t)=>{const n={};for(const s in e)(!Ps(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function oc(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:l,patchFlag:c}=t,a=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?ar(s,i,a):!!i;if(c&8){const u=t.dynamicProps;for(let h=0;h<u.length;h++){const g=u[h];if(i[g]!==s[g]&&!Vn(a,g))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?ar(s,i,a):!0:!!i;return!1}function ar(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!Vn(n,o))return!0}return!1}function ic({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Yo=e=>e.__isSuspense;function lc(e,t){t&&t.pendingBranch?$(e)?t.effects.push(...e):t.effects.push(e):hl(e)}const Ve=Symbol.for("v-fgt"),Kn=Symbol.for("v-txt"),gt=Symbol.for("v-cmt"),ts=Symbol.for("v-stc"),Qt=[];let Re=null;function ke(e=!1){Qt.push(Re=e?null:[])}function cc(){Qt.pop(),Re=Qt[Qt.length-1]||null}let sn=1;function dr(e,t=!1){sn+=e,e<0&&Re&&t&&(Re.hasOnce=!0)}function Qo(e){return e.dynamicChildren=sn>0?Re||It:null,cc(),sn>0&&Re&&Re.push(e),e}function Qe(e,t,n,s,r,o){return Qo(ie(e,t,n,s,r,o,!0))}function Jo(e,t,n,s,r){return Qo(Se(e,t,n,s,r,!0))}function Tn(e){return e?e.__v_isVNode===!0:!1}function Dt(e,t){return e.type===t.type&&e.key===t.key}const Zo=({key:e})=>e??null,bn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ue(e)||le(e)||K(e)?{i:Pe,r:e,k:t,f:!!n}:e:null);function ie(e,t=null,n=null,s=0,r=null,o=e===Ve?0:1,i=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Zo(t),ref:t&&bn(t),scopeId:Io,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Pe};return l?(Ws(c,n),o&128&&e.normalize(c)):n&&(c.shapeFlag|=ue(n)?8:16),sn>0&&!i&&Re&&(c.patchFlag>0||o&6)&&c.patchFlag!==32&&Re.push(c),c}const Se=uc;function uc(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===Tl)&&(e=gt),Tn(e)){const l=kt(e,t,!0);return n&&Ws(l,n),sn>0&&!o&&Re&&(l.shapeFlag&6?Re[Re.indexOf(e)]=l:Re.push(l)),l.patchFlag=-2,l}if(bc(e)&&(e=e.__vccOpts),t){t=fc(t);let{class:l,style:c}=t;l&&!ue(l)&&(t.class=Nn(l)),se(c)&&(Hs(c)&&!$(c)&&(c=pe({},c)),t.style=Is(c))}const i=ue(e)?1:Yo(e)?128:ml(e)?64:se(e)?4:K(e)?2:0;return ie(e,t,n,s,r,i,o,!0)}function fc(e){return e?Hs(e)||jo(e)?pe({},e):e:null}function kt(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:c}=e,a=t?ac(r||{},t):r,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&Zo(a),ref:t&&t.ref?n&&o?$(o)?o.concat(bn(t)):[o,bn(t)]:bn(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ve?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&kt(e.ssContent),ssFallback:e.ssFallback&&kt(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&Ds(u,c.clone(u)),u}function Xo(e=" ",t=0){return Se(Kn,null,e,t)}function wn(e="",t=!1){return t?(ke(),Jo(gt,null,e)):Se(gt,null,e)}function Ke(e){return e==null||typeof e=="boolean"?Se(gt):$(e)?Se(Ve,null,e.slice()):Tn(e)?ut(e):Se(Kn,null,String(e))}function ut(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:kt(e)}function Ws(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if($(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Ws(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!jo(t)?t._ctx=Pe:r===3&&Pe&&(Pe.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else K(t)?(t={default:t,_ctx:Pe},n=32):(t=String(t),s&64?(n=16,t=[Xo(t)]):n=8);e.children=t,e.shapeFlag|=n}function ac(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=Nn([t.class,s.class]));else if(r==="style")t.style=Is([t.style,s.style]);else if(Mn(r)){const o=t[r],i=s[r];i&&o!==i&&!($(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function De(e,t,n,s=null){ze(e,t,7,[n,s])}const dc=Lo();let hc=0;function pc(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||dc,o={uid:hc++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new so(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Do(s,r),emitsOptions:Go(s,r),emit:null,emitted:null,propsDefaults:ee,inheritAttrs:s.inheritAttrs,ctx:ee,data:ee,props:ee,attrs:ee,slots:ee,refs:ee,setupState:ee,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=nc.bind(null,o),e.ce&&e.ce(o),o}let he=null;const ei=()=>he||Pe;let On,vs;{const e=Ln(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};On=t("__VUE_INSTANCE_SETTERS__",n=>he=n),vs=t("__VUE_SSR_SETTERS__",n=>rn=n)}const hn=e=>{const t=he;return On(e),e.scope.on(),()=>{e.scope.off(),On(t)}},hr=()=>{he&&he.scope.off(),On(null)};function ti(e){return e.vnode.shapeFlag&4}let rn=!1;function gc(e,t=!1,n=!1){t&&vs(t);const{props:s,children:r}=e.vnode,o=ti(e);Vl(e,s,o,t),zl(e,r,n||t);const i=o?mc(e,t):void 0;return t&&vs(!1),i}function mc(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,kl);const{setup:s}=n;if(s){tt();const r=e.setupContext=s.length>1?vc(e):null,o=hn(e),i=dn(s,e,0,[e.props,r]),l=Jr(i);if(nt(),o(),(l||e.sp)&&!qt(e)&&To(e),l){if(i.then(hr,hr),t)return i.then(c=>{pr(e,c)}).catch(c=>{jn(c,e,0)});e.asyncDep=i}else pr(e,i)}else ni(e)}function pr(e,t,n){K(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:se(t)&&(e.setupState=Eo(t)),ni(e)}function ni(e,t,n){const s=e.type;e.render||(e.render=s.render||Ue);{const r=hn(e);tt();try{Fl(e)}finally{nt(),r()}}}const yc={get(e,t){return de(e,"get",""),e[t]}};function vc(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,yc),slots:e.slots,emit:e.emit,expose:t}}function Un(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Eo(js(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Gt)return Gt[n](e)},has(t,n){return n in t||n in Gt}})):e.proxy}function _c(e,t=!0){return K(e)?e.displayName||e.name:e.name||t&&e.__name}function bc(e){return K(e)&&"__vccOpts"in e}const ce=(e,t)=>cl(e,t,rn);function si(e,t,n){const s=arguments.length;return s===2?se(t)&&!$(t)?Tn(t)?Se(e,null,[t]):Se(e,t):Se(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&Tn(n)&&(n=[n]),Se(e,t,n))}const wc="3.5.18";/**
* @vue/runtime-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let _s;const gr=typeof window<"u"&&window.trustedTypes;if(gr)try{_s=gr.createPolicy("vue",{createHTML:e=>e})}catch{}const ri=_s?e=>_s.createHTML(e):e=>e,Sc="http://www.w3.org/2000/svg",xc="http://www.w3.org/1998/Math/MathML",Je=typeof document<"u"?document:null,mr=Je&&Je.createElement("template"),Ec={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?Je.createElementNS(Sc,e):t==="mathml"?Je.createElementNS(xc,e):n?Je.createElement(e,{is:n}):Je.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>Je.createTextNode(e),createComment:e=>Je.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Je.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{mr.innerHTML=ri(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=mr.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Cc=Symbol("_vtc");function Pc(e,t,n){const s=e[Cc];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const yr=Symbol("_vod"),Rc=Symbol("_vsh"),Ac=Symbol(""),Ic=/(^|;)\s*display\s*:/;function Tc(e,t,n){const s=e.style,r=ue(n);let o=!1;if(n&&!r){if(t)if(ue(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&Sn(s,l,"")}else for(const i in t)n[i]==null&&Sn(s,i,"");for(const i in n)i==="display"&&(o=!0),Sn(s,i,n[i])}else if(r){if(t!==n){const i=s[Ac];i&&(n+=";"+i),s.cssText=n,o=Ic.test(n)}}else t&&e.removeAttribute("style");yr in e&&(e[yr]=o?s.display:"",e[Rc]&&(s.display="none"))}const vr=/\s*!important$/;function Sn(e,t,n){if($(n))n.forEach(s=>Sn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Oc(e,t);vr.test(n)?e.setProperty(St(s),n.replace(vr,""),"important"):e[s]=n}}const _r=["Webkit","Moz","ms"],ns={};function Oc(e,t){const n=ns[t];if(n)return n;let s=Me(t);if(s!=="filter"&&s in e)return ns[t]=s;s=Fn(s);for(let r=0;r<_r.length;r++){const o=_r[r]+s;if(o in e)return ns[t]=o}return t}const br="http://www.w3.org/1999/xlink";function wr(e,t,n,s,r,o=ki(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(br,t.slice(6,t.length)):e.setAttributeNS(br,t,n):n==null||o&&!eo(n)?e.removeAttribute(t):e.setAttribute(t,o?"":We(n)?String(n):n)}function Sr(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?ri(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=eo(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function at(e,t,n,s){e.addEventListener(t,n,s)}function Mc(e,t,n,s){e.removeEventListener(t,n,s)}const xr=Symbol("_vei");function kc(e,t,n,s,r=null){const o=e[xr]||(e[xr]={}),i=o[t];if(s&&i)i.value=s;else{const[l,c]=Fc(t);if(s){const a=o[t]=Hc(s,r);at(e,l,a,c)}else i&&(Mc(e,l,i,c),o[t]=void 0)}}const Er=/(?:Once|Passive|Capture)$/;function Fc(e){let t;if(Er.test(e)){t={};let s;for(;s=e.match(Er);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):St(e.slice(2)),t]}let ss=0;const Lc=Promise.resolve(),Nc=()=>ss||(Lc.then(()=>ss=0),ss=Date.now());function Hc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;ze(jc(s,n.value),t,5,[s])};return n.value=e,n.attached=Nc(),n}function jc(e,t){if($(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const Cr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,$c=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?Pc(e,s,i):t==="style"?Tc(e,n,s):Mn(t)?Ps(t)||kc(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Dc(e,t,s,i))?(Sr(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&wr(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ue(s))?Sr(e,Me(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),wr(e,t,s,i))};function Dc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Cr(t)&&K(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return Cr(t)&&ue(n)?!1:t in e}const Ft=e=>{const t=e.props["onUpdate:modelValue"]||!1;return $(t)?n=>vn(t,n):t};function Bc(e){e.target.composing=!0}function Pr(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const et=Symbol("_assign"),kf={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[et]=Ft(r);const o=s||r.props&&r.props.type==="number";at(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=xn(l)),e[et](l)}),n&&at(e,"change",()=>{e.value=e.value.trim()}),t||(at(e,"compositionstart",Bc),at(e,"compositionend",Pr),at(e,"change",Pr))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[et]=Ft(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?xn(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===c)||(e.value=c))}},Ff={deep:!0,created(e,t,n){e[et]=Ft(n),at(e,"change",()=>{const s=e._modelValue,r=on(e),o=e.checked,i=e[et];if($(s)){const l=Ts(s,r),c=l!==-1;if(o&&!c)i(s.concat(r));else if(!o&&c){const a=[...s];a.splice(l,1),i(a)}}else if(Ht(s)){const l=new Set(s);o?l.add(r):l.delete(r),i(l)}else i(oi(e,o))})},mounted:Rr,beforeUpdate(e,t,n){e[et]=Ft(n),Rr(e,t,n)}};function Rr(e,{value:t,oldValue:n},s){e._modelValue=t;let r;if($(t))r=Ts(t,s.props.value)>-1;else if(Ht(t))r=t.has(s.props.value);else{if(t===n)return;r=fn(t,oi(e,!0))}e.checked!==r&&(e.checked=r)}const Lf={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=Ht(t);at(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?xn(on(i)):on(i));e[et](e.multiple?r?new Set(o):o:o[0]),e._assigning=!0,$n(()=>{e._assigning=!1})}),e[et]=Ft(s)},mounted(e,{value:t}){Ar(e,t)},beforeUpdate(e,t,n){e[et]=Ft(n)},updated(e,{value:t}){e._assigning||Ar(e,t)}};function Ar(e,t){const n=e.multiple,s=$(t);if(!(n&&!s&&!Ht(t))){for(let r=0,o=e.options.length;r<o;r++){const i=e.options[r],l=on(i);if(n)if(s){const c=typeof l;c==="string"||c==="number"?i.selected=t.some(a=>String(a)===String(l)):i.selected=Ts(t,l)>-1}else i.selected=t.has(l);else if(fn(on(i),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function on(e){return"_value"in e?e._value:e.value}function oi(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const Vc=["ctrl","shift","alt","meta"],Kc={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Vc.some(n=>e[`${n}Key`]&&!t.includes(n))},Nf=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...o)=>{for(let i=0;i<t.length;i++){const l=Kc[t[i]];if(l&&l(r,t))return}return e(r,...o)})},Uc=pe({patchProp:$c},Ec);let Ir;function Wc(){return Ir||(Ir=Gl(Uc))}const zc=(...e)=>{const t=Wc().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=Gc(s);if(!r)return;const o=t._component;!K(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,qc(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function qc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Gc(e){return ue(e)?document.querySelector(e):e}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let ii;const Wn=e=>ii=e,li=Symbol();function bs(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var Jt;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Jt||(Jt={}));function Yc(){const e=ro(!0),t=e.run(()=>Ie({}));let n=[],s=[];const r=js({install(o){Wn(r),r._a=o,o.provide(li,r),o.config.globalProperties.$pinia=r,s.forEach(i=>n.push(i)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const ci=()=>{};function Tr(e,t,n,s=ci){e.push(t);const r=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),s())};return!n&&oo()&&Li(r),r}function Pt(e,...t){e.slice().forEach(n=>{n(...t)})}const Qc=e=>e(),Or=Symbol(),rs=Symbol();function ws(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,s)=>e.set(s,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const s=t[n],r=e[n];bs(r)&&bs(s)&&e.hasOwnProperty(n)&&!le(s)&&!ht(s)?e[n]=ws(r,s):e[n]=s}return e}const Jc=Symbol();function Zc(e){return!bs(e)||!e.hasOwnProperty(Jc)}const{assign:lt}=Object;function Xc(e){return!!(le(e)&&e.effect)}function eu(e,t,n,s){const{state:r,actions:o,getters:i}=t,l=n.state.value[e];let c;function a(){l||(n.state.value[e]=r?r():{});const u=rl(n.state.value[e]);return lt(u,o,Object.keys(i||{}).reduce((h,g)=>(h[g]=js(ce(()=>{Wn(n);const m=n._s.get(e);return i[g].call(m,m)})),h),{}))}return c=ui(e,a,t,n,s,!0),c}function ui(e,t,n={},s,r,o){let i;const l=lt({actions:{}},n),c={deep:!0};let a,u,h=[],g=[],m;const C=s.state.value[e];!o&&!C&&(s.state.value[e]={}),Ie({});let I;function B(D){let W;a=u=!1,typeof D=="function"?(D(s.state.value[e]),W={type:Jt.patchFunction,storeId:e,events:m}):(ws(s.state.value[e],D),W={type:Jt.patchObject,payload:D,storeId:e,events:m});const oe=I=Symbol();$n().then(()=>{I===oe&&(a=!0)}),u=!0,Pt(h,W,s.state.value[e])}const N=o?function(){const{state:W}=n,oe=W?W():{};this.$patch(ge=>{lt(ge,oe)})}:ci;function F(){i.stop(),h=[],g=[],s._s.delete(e)}const S=(D,W="")=>{if(Or in D)return D[rs]=W,D;const oe=function(){Wn(s);const ge=Array.from(arguments),xe=[],ve=[];function mt(U){xe.push(U)}function rt(U){ve.push(U)}Pt(g,{args:ge,name:oe[rs],store:R,after:mt,onError:rt});let re;try{re=D.apply(this&&this.$id===e?this:R,ge)}catch(U){throw Pt(ve,U),U}return re instanceof Promise?re.then(U=>(Pt(xe,U),U)).catch(U=>(Pt(ve,U),Promise.reject(U))):(Pt(xe,re),re)};return oe[Or]=!0,oe[rs]=W,oe},w={_p:s,$id:e,$onAction:Tr.bind(null,g),$patch:B,$reset:N,$subscribe(D,W={}){const oe=Tr(h,D,W.detached,()=>ge()),ge=i.run(()=>Yt(()=>s.state.value[e],xe=>{(W.flush==="sync"?u:a)&&D({storeId:e,type:Jt.direct,events:m},xe)},lt({},c,W)));return oe},$dispose:F},R=an(w);s._s.set(e,R);const A=(s._a&&s._a.runWithContext||Qc)(()=>s._e.run(()=>(i=ro()).run(()=>t({action:S}))));for(const D in A){const W=A[D];if(le(W)&&!Xc(W)||ht(W))o||(C&&Zc(W)&&(le(W)?W.value=C[D]:ws(W,C[D])),s.state.value[e][D]=W);else if(typeof W=="function"){const oe=S(W,D);A[D]=oe,l.actions[D]=W}}return lt(R,A),lt(G(R),A),Object.defineProperty(R,"$state",{get:()=>s.state.value[e],set:D=>{B(W=>{lt(W,D)})}}),s._p.forEach(D=>{lt(R,i.run(()=>D({store:R,app:s._a,pinia:s,options:l})))}),C&&o&&n.hydrate&&n.hydrate(R.$state,C),a=!0,u=!0,R}/*! #__NO_SIDE_EFFECTS__ */function fi(e,t,n){let s,r;const o=typeof t=="function";typeof e=="string"?(s=e,r=o?n:t):(r=e,s=e.id);function i(l,c){const a=Bl();return l=l||(a?Oe(li,null):null),l&&Wn(l),l=ii,l._s.has(s)||(o?ui(s,t,r,l):eu(s,r,l)),l._s.get(s)}return i.$id=s,i}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const At=typeof document<"u";function ai(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function tu(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&ai(e.default)}const Q=Object.assign;function os(e,t){const n={};for(const s in t){const r=t[s];n[s]=Le(r)?r.map(e):e(r)}return n}const Zt=()=>{},Le=Array.isArray,di=/#/g,nu=/&/g,su=/\//g,ru=/=/g,ou=/\?/g,hi=/\+/g,iu=/%5B/g,lu=/%5D/g,pi=/%5E/g,cu=/%60/g,gi=/%7B/g,uu=/%7C/g,mi=/%7D/g,fu=/%20/g;function zs(e){return encodeURI(""+e).replace(uu,"|").replace(iu,"[").replace(lu,"]")}function au(e){return zs(e).replace(gi,"{").replace(mi,"}").replace(pi,"^")}function Ss(e){return zs(e).replace(hi,"%2B").replace(fu,"+").replace(di,"%23").replace(nu,"%26").replace(cu,"`").replace(gi,"{").replace(mi,"}").replace(pi,"^")}function du(e){return Ss(e).replace(ru,"%3D")}function hu(e){return zs(e).replace(di,"%23").replace(ou,"%3F")}function pu(e){return e==null?"":hu(e).replace(su,"%2F")}function ln(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const gu=/\/$/,mu=e=>e.replace(gu,"");function is(e,t,n="/"){let s,r={},o="",i="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),o=t.slice(c+1,l>-1?l:t.length),r=e(o)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=bu(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:ln(i)}}function yu(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Mr(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function vu(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Lt(t.matched[s],n.matched[r])&&yi(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Lt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function yi(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!_u(e[n],t[n]))return!1;return!0}function _u(e,t){return Le(e)?kr(e,t):Le(t)?kr(t,e):e===t}function kr(e,t){return Le(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function bu(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const it={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var cn;(function(e){e.pop="pop",e.push="push"})(cn||(cn={}));var Xt;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Xt||(Xt={}));function wu(e){if(!e)if(At){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),mu(e)}const Su=/^[^#]+#/;function xu(e,t){return e.replace(Su,"#")+t}function Eu(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const zn=()=>({left:window.scrollX,top:window.scrollY});function Cu(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=Eu(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Fr(e,t){return(history.state?history.state.position-t:-1)+e}const xs=new Map;function Pu(e,t){xs.set(e,t)}function Ru(e){const t=xs.get(e);return xs.delete(e),t}let Au=()=>location.protocol+"//"+location.host;function vi(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,c=r.slice(l);return c[0]!=="/"&&(c="/"+c),Mr(c,"")}return Mr(n,e)+s+r}function Iu(e,t,n,s){let r=[],o=[],i=null;const l=({state:g})=>{const m=vi(e,location),C=n.value,I=t.value;let B=0;if(g){if(n.value=m,t.value=g,i&&i===C){i=null;return}B=I?g.position-I.position:0}else s(m);r.forEach(N=>{N(n.value,C,{delta:B,type:cn.pop,direction:B?B>0?Xt.forward:Xt.back:Xt.unknown})})};function c(){i=n.value}function a(g){r.push(g);const m=()=>{const C=r.indexOf(g);C>-1&&r.splice(C,1)};return o.push(m),m}function u(){const{history:g}=window;g.state&&g.replaceState(Q({},g.state,{scroll:zn()}),"")}function h(){for(const g of o)g();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:c,listen:a,destroy:h}}function Lr(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?zn():null}}function Tu(e){const{history:t,location:n}=window,s={value:vi(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(c,a,u){const h=e.indexOf("#"),g=h>-1?(n.host&&document.querySelector("base")?e:e.slice(h))+c:Au()+e+c;try{t[u?"replaceState":"pushState"](a,"",g),r.value=a}catch(m){console.error(m),n[u?"replace":"assign"](g)}}function i(c,a){const u=Q({},t.state,Lr(r.value.back,c,r.value.forward,!0),a,{position:r.value.position});o(c,u,!0),s.value=c}function l(c,a){const u=Q({},r.value,t.state,{forward:c,scroll:zn()});o(u.current,u,!0);const h=Q({},Lr(s.value,c,null),{position:u.position+1},a);o(c,h,!1),s.value=c}return{location:s,state:r,push:l,replace:i}}function Ou(e){e=wu(e);const t=Tu(e),n=Iu(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=Q({location:"",base:e,go:s,createHref:xu.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Mu(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),Ou(e)}function ku(e){return typeof e=="string"||e&&typeof e=="object"}function _i(e){return typeof e=="string"||typeof e=="symbol"}const bi=Symbol("");var Nr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Nr||(Nr={}));function Nt(e,t){return Q(new Error,{type:e,[bi]:!0},t)}function Ye(e,t){return e instanceof Error&&bi in e&&(t==null||!!(e.type&t))}const Hr="[^/]+?",Fu={sensitive:!1,strict:!1,start:!0,end:!0},Lu=/[.+*?^${}()[\]/\\]/g;function Nu(e,t){const n=Q({},Fu,t),s=[];let r=n.start?"^":"";const o=[];for(const a of e){const u=a.length?[]:[90];n.strict&&!a.length&&(r+="/");for(let h=0;h<a.length;h++){const g=a[h];let m=40+(n.sensitive?.25:0);if(g.type===0)h||(r+="/"),r+=g.value.replace(Lu,"\\$&"),m+=40;else if(g.type===1){const{value:C,repeatable:I,optional:B,regexp:N}=g;o.push({name:C,repeatable:I,optional:B});const F=N||Hr;if(F!==Hr){m+=10;try{new RegExp(`(${F})`)}catch(w){throw new Error(`Invalid custom RegExp for param "${C}" (${F}): `+w.message)}}let S=I?`((?:${F})(?:/(?:${F}))*)`:`(${F})`;h||(S=B&&a.length<2?`(?:/${S})`:"/"+S),B&&(S+="?"),r+=S,m+=20,B&&(m+=-8),I&&(m+=-20),F===".*"&&(m+=-50)}u.push(m)}s.push(u)}if(n.strict&&n.end){const a=s.length-1;s[a][s[a].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function l(a){const u=a.match(i),h={};if(!u)return null;for(let g=1;g<u.length;g++){const m=u[g]||"",C=o[g-1];h[C.name]=m&&C.repeatable?m.split("/"):m}return h}function c(a){let u="",h=!1;for(const g of e){(!h||!u.endsWith("/"))&&(u+="/"),h=!1;for(const m of g)if(m.type===0)u+=m.value;else if(m.type===1){const{value:C,repeatable:I,optional:B}=m,N=C in a?a[C]:"";if(Le(N)&&!I)throw new Error(`Provided param "${C}" is an array but it is not repeatable (* or + modifiers)`);const F=Le(N)?N.join("/"):N;if(!F)if(B)g.length<2&&(u.endsWith("/")?u=u.slice(0,-1):h=!0);else throw new Error(`Missing required param "${C}"`);u+=F}}return u||"/"}return{re:i,score:s,keys:o,parse:l,stringify:c}}function Hu(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function wi(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=Hu(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(jr(s))return 1;if(jr(r))return-1}return r.length-s.length}function jr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ju={type:0,value:""},$u=/[a-zA-Z0-9_]/;function Du(e){if(!e)return[[]];if(e==="/")return[[ju]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${a}": ${m}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,c,a="",u="";function h(){a&&(n===0?o.push({type:0,value:a}):n===1||n===2||n===3?(o.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${a}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:a,regexp:u,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),a="")}function g(){a+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(a&&h(),i()):c===":"?(h(),n=1):g();break;case 4:g(),n=s;break;case 1:c==="("?n=2:$u.test(c)?g():(h(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+c:n=3:u+=c;break;case 3:h(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${a}"`),h(),i(),r}function Bu(e,t,n){const s=Nu(Du(e.path),n),r=Q(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Vu(e,t){const n=[],s=new Map;t=Vr({strict:!1,end:!0,sensitive:!1},t);function r(h){return s.get(h)}function o(h,g,m){const C=!m,I=Dr(h);I.aliasOf=m&&m.record;const B=Vr(t,h),N=[I];if("alias"in h){const w=typeof h.alias=="string"?[h.alias]:h.alias;for(const R of w)N.push(Dr(Q({},I,{components:m?m.record.components:I.components,path:R,aliasOf:m?m.record:I})))}let F,S;for(const w of N){const{path:R}=w;if(g&&R[0]!=="/"){const z=g.record.path,A=z[z.length-1]==="/"?"":"/";w.path=g.record.path+(R&&A+R)}if(F=Bu(w,g,B),m?m.alias.push(F):(S=S||F,S!==F&&S.alias.push(F),C&&h.name&&!Br(F)&&i(h.name)),Si(F)&&c(F),I.children){const z=I.children;for(let A=0;A<z.length;A++)o(z[A],F,m&&m.children[A])}m=m||F}return S?()=>{i(S)}:Zt}function i(h){if(_i(h)){const g=s.get(h);g&&(s.delete(h),n.splice(n.indexOf(g),1),g.children.forEach(i),g.alias.forEach(i))}else{const g=n.indexOf(h);g>-1&&(n.splice(g,1),h.record.name&&s.delete(h.record.name),h.children.forEach(i),h.alias.forEach(i))}}function l(){return n}function c(h){const g=Wu(h,n);n.splice(g,0,h),h.record.name&&!Br(h)&&s.set(h.record.name,h)}function a(h,g){let m,C={},I,B;if("name"in h&&h.name){if(m=s.get(h.name),!m)throw Nt(1,{location:h});B=m.record.name,C=Q($r(g.params,m.keys.filter(S=>!S.optional).concat(m.parent?m.parent.keys.filter(S=>S.optional):[]).map(S=>S.name)),h.params&&$r(h.params,m.keys.map(S=>S.name))),I=m.stringify(C)}else if(h.path!=null)I=h.path,m=n.find(S=>S.re.test(I)),m&&(C=m.parse(I),B=m.record.name);else{if(m=g.name?s.get(g.name):n.find(S=>S.re.test(g.path)),!m)throw Nt(1,{location:h,currentLocation:g});B=m.record.name,C=Q({},g.params,h.params),I=m.stringify(C)}const N=[];let F=m;for(;F;)N.unshift(F.record),F=F.parent;return{name:B,path:I,params:C,matched:N,meta:Uu(N)}}e.forEach(h=>o(h));function u(){n.length=0,s.clear()}return{addRoute:o,resolve:a,removeRoute:i,clearRoutes:u,getRoutes:l,getRecordMatcher:r}}function $r(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Dr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Ku(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Ku(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Br(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Uu(e){return e.reduce((t,n)=>Q(t,n.meta),{})}function Vr(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function Wu(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;wi(e,t[o])<0?s=o:n=o+1}const r=zu(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function zu(e){let t=e;for(;t=t.parent;)if(Si(t)&&wi(e,t)===0)return t}function Si({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function qu(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(hi," "),i=o.indexOf("="),l=ln(i<0?o:o.slice(0,i)),c=i<0?null:ln(o.slice(i+1));if(l in t){let a=t[l];Le(a)||(a=t[l]=[a]),a.push(c)}else t[l]=c}return t}function Kr(e){let t="";for(let n in e){const s=e[n];if(n=du(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Le(s)?s.map(o=>o&&Ss(o)):[s&&Ss(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Gu(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Le(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const Yu=Symbol(""),Ur=Symbol(""),qn=Symbol(""),qs=Symbol(""),Es=Symbol("");function Bt(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function ft(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,c)=>{const a=g=>{g===!1?c(Nt(4,{from:n,to:t})):g instanceof Error?c(g):ku(g)?c(Nt(2,{from:t,to:g})):(i&&s.enterCallbacks[r]===i&&typeof g=="function"&&i.push(g),l())},u=o(()=>e.call(s&&s.instances[r],t,n,a));let h=Promise.resolve(u);e.length<3&&(h=h.then(a)),h.catch(g=>c(g))})}function ls(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let c=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(ai(c)){const u=(c.__vccOpts||c)[t];u&&o.push(ft(u,n,s,i,l,r))}else{let a=c();o.push(()=>a.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const h=tu(u)?u.default:u;i.mods[l]=u,i.components[l]=h;const m=(h.__vccOpts||h)[t];return m&&ft(m,n,s,i,l,r)()}))}}return o}function Wr(e){const t=Oe(qn),n=Oe(qs),s=ce(()=>{const c=Ot(e.to);return t.resolve(c)}),r=ce(()=>{const{matched:c}=s.value,{length:a}=c,u=c[a-1],h=n.matched;if(!u||!h.length)return-1;const g=h.findIndex(Lt.bind(null,u));if(g>-1)return g;const m=zr(c[a-2]);return a>1&&zr(u)===m&&h[h.length-1].path!==m?h.findIndex(Lt.bind(null,c[a-2])):g}),o=ce(()=>r.value>-1&&ef(n.params,s.value.params)),i=ce(()=>r.value>-1&&r.value===n.matched.length-1&&yi(n.params,s.value.params));function l(c={}){if(Xu(c)){const a=t[Ot(e.replace)?"replace":"push"](Ot(e.to)).catch(Zt);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>a),a}return Promise.resolve()}return{route:s,href:ce(()=>s.value.href),isActive:o,isExactActive:i,navigate:l}}function Qu(e){return e.length===1?e[0]:e}const Ju=Dn({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Wr,setup(e,{slots:t}){const n=an(Wr(e)),{options:s}=Oe(qn),r=ce(()=>({[qr(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[qr(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Qu(t.default(n));return e.custom?o:si("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),Zu=Ju;function Xu(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function ef(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!Le(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function zr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const qr=(e,t,n)=>e??t??n,tf=Dn({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=Oe(Es),r=ce(()=>e.route||s.value),o=Oe(Ur,0),i=ce(()=>{let a=Ot(o);const{matched:u}=r.value;let h;for(;(h=u[a])&&!h.components;)a++;return a}),l=ce(()=>r.value.matched[i.value]);_n(Ur,ce(()=>i.value+1)),_n(Yu,l),_n(Es,r);const c=Ie();return Yt(()=>[c.value,l.value,e.name],([a,u,h],[g,m,C])=>{u&&(u.instances[h]=a,m&&m!==u&&a&&a===g&&(u.leaveGuards.size||(u.leaveGuards=m.leaveGuards),u.updateGuards.size||(u.updateGuards=m.updateGuards))),a&&u&&(!m||!Lt(u,m)||!g)&&(u.enterCallbacks[h]||[]).forEach(I=>I(a))},{flush:"post"}),()=>{const a=r.value,u=e.name,h=l.value,g=h&&h.components[u];if(!g)return Gr(n.default,{Component:g,route:a});const m=h.props[u],C=m?m===!0?a.params:typeof m=="function"?m(a):m:null,B=si(g,Q({},C,t,{onVnodeUnmounted:N=>{N.component.isUnmounted&&(h.instances[u]=null)},ref:c}));return Gr(n.default,{Component:B,route:a})||B}}});function Gr(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const nf=tf;function sf(e){const t=Vu(e.routes,e),n=e.parseQuery||qu,s=e.stringifyQuery||Kr,r=e.history,o=Bt(),i=Bt(),l=Bt(),c=tl(it);let a=it;At&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=os.bind(null,v=>""+v),h=os.bind(null,pu),g=os.bind(null,ln);function m(v,k){let O,L;return _i(v)?(O=t.getRecordMatcher(v),L=k):L=v,t.addRoute(L,O)}function C(v){const k=t.getRecordMatcher(v);k&&t.removeRoute(k)}function I(){return t.getRoutes().map(v=>v.record)}function B(v){return!!t.getRecordMatcher(v)}function N(v,k){if(k=Q({},k||c.value),typeof v=="string"){const p=is(n,v,k.path),y=t.resolve({path:p.path},k),b=r.createHref(p.fullPath);return Q(p,y,{params:g(y.params),hash:ln(p.hash),redirectedFrom:void 0,href:b})}let O;if(v.path!=null)O=Q({},v,{path:is(n,v.path,k.path).path});else{const p=Q({},v.params);for(const y in p)p[y]==null&&delete p[y];O=Q({},v,{params:h(p)}),k.params=h(k.params)}const L=t.resolve(O,k),X=v.hash||"";L.params=u(g(L.params));const f=yu(s,Q({},v,{hash:au(X),path:L.path})),d=r.createHref(f);return Q({fullPath:f,hash:X,query:s===Kr?Gu(v.query):v.query||{}},L,{redirectedFrom:void 0,href:d})}function F(v){return typeof v=="string"?is(n,v,c.value.path):Q({},v)}function S(v,k){if(a!==v)return Nt(8,{from:k,to:v})}function w(v){return A(v)}function R(v){return w(Q(F(v),{replace:!0}))}function z(v){const k=v.matched[v.matched.length-1];if(k&&k.redirect){const{redirect:O}=k;let L=typeof O=="function"?O(v):O;return typeof L=="string"&&(L=L.includes("?")||L.includes("#")?L=F(L):{path:L},L.params={}),Q({query:v.query,hash:v.hash,params:L.path!=null?{}:v.params},L)}}function A(v,k){const O=a=N(v),L=c.value,X=v.state,f=v.force,d=v.replace===!0,p=z(O);if(p)return A(Q(F(p),{state:typeof p=="object"?Q({},X,p.state):X,force:f,replace:d}),k||O);const y=O;y.redirectedFrom=k;let b;return!f&&vu(s,L,O)&&(b=Nt(16,{to:y,from:L}),Ne(L,L,!0,!1)),(b?Promise.resolve(b):oe(y,L)).catch(_=>Ye(_)?Ye(_,2)?_:ot(_):Y(_,y,L)).then(_=>{if(_){if(Ye(_,2))return A(Q({replace:d},F(_.to),{state:typeof _.to=="object"?Q({},X,_.to.state):X,force:f}),k||y)}else _=xe(y,L,!0,d,X);return ge(y,L,_),_})}function D(v,k){const O=S(v,k);return O?Promise.reject(O):Promise.resolve()}function W(v){const k=Et.values().next().value;return k&&typeof k.runWithContext=="function"?k.runWithContext(v):v()}function oe(v,k){let O;const[L,X,f]=rf(v,k);O=ls(L.reverse(),"beforeRouteLeave",v,k);for(const p of L)p.leaveGuards.forEach(y=>{O.push(ft(y,v,k))});const d=D.bind(null,v,k);return O.push(d),Ae(O).then(()=>{O=[];for(const p of o.list())O.push(ft(p,v,k));return O.push(d),Ae(O)}).then(()=>{O=ls(X,"beforeRouteUpdate",v,k);for(const p of X)p.updateGuards.forEach(y=>{O.push(ft(y,v,k))});return O.push(d),Ae(O)}).then(()=>{O=[];for(const p of f)if(p.beforeEnter)if(Le(p.beforeEnter))for(const y of p.beforeEnter)O.push(ft(y,v,k));else O.push(ft(p.beforeEnter,v,k));return O.push(d),Ae(O)}).then(()=>(v.matched.forEach(p=>p.enterCallbacks={}),O=ls(f,"beforeRouteEnter",v,k,W),O.push(d),Ae(O))).then(()=>{O=[];for(const p of i.list())O.push(ft(p,v,k));return O.push(d),Ae(O)}).catch(p=>Ye(p,8)?p:Promise.reject(p))}function ge(v,k,O){l.list().forEach(L=>W(()=>L(v,k,O)))}function xe(v,k,O,L,X){const f=S(v,k);if(f)return f;const d=k===it,p=At?history.state:{};O&&(L||d?r.replace(v.fullPath,Q({scroll:d&&p&&p.scroll},X)):r.push(v.fullPath,X)),c.value=v,Ne(v,k,O,d),ot()}let ve;function mt(){ve||(ve=r.listen((v,k,O)=>{if(!pn.listening)return;const L=N(v),X=z(L);if(X){A(Q(X,{replace:!0,force:!0}),L).catch(Zt);return}a=L;const f=c.value;At&&Pu(Fr(f.fullPath,O.delta),zn()),oe(L,f).catch(d=>Ye(d,12)?d:Ye(d,2)?(A(Q(F(d.to),{force:!0}),L).then(p=>{Ye(p,20)&&!O.delta&&O.type===cn.pop&&r.go(-1,!1)}).catch(Zt),Promise.reject()):(O.delta&&r.go(-O.delta,!1),Y(d,L,f))).then(d=>{d=d||xe(L,f,!1),d&&(O.delta&&!Ye(d,8)?r.go(-O.delta,!1):O.type===cn.pop&&Ye(d,20)&&r.go(-1,!1)),ge(L,f,d)}).catch(Zt)}))}let rt=Bt(),re=Bt(),U;function Y(v,k,O){ot(v);const L=re.list();return L.length?L.forEach(X=>X(v,k,O)):console.error(v),Promise.reject(v)}function qe(){return U&&c.value!==it?Promise.resolve():new Promise((v,k)=>{rt.add([v,k])})}function ot(v){return U||(U=!v,mt(),rt.list().forEach(([k,O])=>v?O(v):k()),rt.reset()),v}function Ne(v,k,O,L){const{scrollBehavior:X}=e;if(!At||!X)return Promise.resolve();const f=!O&&Ru(Fr(v.fullPath,0))||(L||!O)&&history.state&&history.state.scroll||null;return $n().then(()=>X(v,k,f)).then(d=>d&&Cu(d)).catch(d=>Y(d,v,k))}const _e=v=>r.go(v);let xt;const Et=new Set,pn={currentRoute:c,listening:!0,addRoute:m,removeRoute:C,clearRoutes:t.clearRoutes,hasRoute:B,getRoutes:I,resolve:N,options:e,push:w,replace:R,go:_e,back:()=>_e(-1),forward:()=>_e(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:re.add,isReady:qe,install(v){const k=this;v.component("RouterLink",Zu),v.component("RouterView",nf),v.config.globalProperties.$router=k,Object.defineProperty(v.config.globalProperties,"$route",{enumerable:!0,get:()=>Ot(c)}),At&&!xt&&c.value===it&&(xt=!0,w(r.location).catch(X=>{}));const O={};for(const X in it)Object.defineProperty(O,X,{get:()=>c.value[X],enumerable:!0});v.provide(qn,k),v.provide(qs,wo(O)),v.provide(Es,c);const L=v.unmount;Et.add(v),v.unmount=function(){Et.delete(v),Et.size<1&&(a=it,ve&&ve(),ve=null,c.value=it,xt=!1,U=!1),L()}}};function Ae(v){return v.reduce((k,O)=>k.then(()=>W(O)),Promise.resolve())}return pn}function rf(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(a=>Lt(a,l))?s.push(l):n.push(l));const c=e.matched[i];c&&(t.matched.find(a=>Lt(a,c))||r.push(c))}return[n,s,r]}function of(){return Oe(qn)}function lf(e){return Oe(qs)}const cf={fontSize:16,fontFamily:"Microsoft YaHei, SimSun, serif",lineHeight:1.8,theme:"light",backgroundColor:"#ffffff",textColor:"#333333",focusMode:!1,typewriterMode:!1,zenMode:!1},Yr={editor:cf,autoSave:!0,autoSaveInterval:3e4,language:"zh-CN",windowSize:{width:1200,height:800}},Hf=[{name:"微软雅黑",value:"Microsoft YaHei, sans-serif"},{name:"宋体",value:"SimSun, serif"},{name:"黑体",value:"SimHei, sans-serif"},{name:"楷体",value:"KaiTi, serif"},{name:"Times New Roman",value:"Times New Roman, serif"},{name:"Arial",value:"Arial, sans-serif"},{name:"Helvetica",value:"Helvetica, sans-serif"}],uf=fi("settings",()=>{const e=Ie({...Yr}),t=Ie(!1),n=Ie(!1),s=ce(()=>e.value.editor),r=ce(()=>e.value.editor.theme);async function o(){try{if(n.value=!0,window.electronAPI){const A=await window.electronAPI.settings.get();e.value={...A}}}catch(A){console.error("Failed to load settings:",A)}finally{n.value=!1}}async function i(A){try{const D={...e.value,...A};if(window.electronAPI){const W=await window.electronAPI.settings.update(D);e.value=W}else e.value=D}catch(D){throw console.error("Failed to update settings:",D),D}}async function l(A){const D={...e.value,editor:{...e.value.editor,...A}};await i(D)}async function c(A){await l({theme:A})}async function a(A){await l({fontFamily:A})}async function u(A){await l({fontSize:A})}async function h(A){await l({lineHeight:A})}async function g(A){await l({backgroundColor:A})}async function m(A){await l({textColor:A})}async function C(){await l({focusMode:!e.value.editor.focusMode})}async function I(){await l({typewriterMode:!e.value.editor.typewriterMode})}async function B(){await l({zenMode:!e.value.editor.zenMode})}async function N(A){await i({autoSave:A})}async function F(A){await i({autoSaveInterval:A})}async function S(A,D){await i({windowSize:{width:A,height:D}})}async function w(A){await i({language:A})}function R(A){t.value=A}async function z(){await i(Yr)}return{settings:e,isFullscreen:t,isLoading:n,editorSettings:s,currentTheme:r,loadSettings:o,updateSettings:i,updateEditorSettings:l,setTheme:c,setFontFamily:a,setFontSize:u,setLineHeight:h,setBackgroundColor:g,setTextColor:m,toggleFocusMode:C,toggleTypewriterMode:I,toggleZenMode:B,setAutoSave:N,setAutoSaveInterval:F,setWindowSize:S,setFullscreen:R,setLanguage:w,resetSettings:z}}),ff=fi("books",()=>{const e=Ie([]),t=Ie([]),n=Ie(null),s=Ie(null),r=Ie(!1),o=ce(()=>n.value?t.value.filter(S=>S.bookId===n.value.id).sort((S,w)=>S.order-w.order):[]),i=ce(()=>e.value.reduce((S,w)=>S+w.wordCount,0));async function l(){try{r.value=!0,window.electronAPI&&(e.value=await window.electronAPI.book.getAll())}catch(S){console.error("Failed to load books:",S)}finally{r.value=!1}}async function c(S){try{if(window.electronAPI){const w=await window.electronAPI.book.create(S);return e.value.push(w),w}}catch(w){throw console.error("Failed to create book:",w),w}}async function a(S,w){var R;try{if(window.electronAPI){const z=await window.electronAPI.book.update(S,w),A=e.value.findIndex(D=>D.id===S);return A!==-1&&(e.value[A]=z),((R=n.value)==null?void 0:R.id)===S&&(n.value=z),z}}catch(z){throw console.error("Failed to update book:",z),z}}async function u(S){var w;try{window.electronAPI&&(await window.electronAPI.book.delete(S),e.value=e.value.filter(R=>R.id!==S),t.value=t.value.filter(R=>R.bookId!==S),((w=n.value)==null?void 0:w.id)===S&&(n.value=null,s.value=null))}catch(R){throw console.error("Failed to delete book:",R),R}}async function h(S){try{if(window.electronAPI){const w=await window.electronAPI.book.getById(S);n.value=w,w&&await g(S)}}catch(w){throw console.error("Failed to set current book:",w),w}}async function g(S){try{if(window.electronAPI){const w=await window.electronAPI.chapter.getByBookId(S);t.value=[...t.value.filter(R=>R.bookId!==S),...w]}}catch(w){console.error("Failed to load chapters:",w)}}async function m(S){var w;try{if(window.electronAPI){const R=await window.electronAPI.chapter.create(S);return t.value.push(R),((w=n.value)==null?void 0:w.id)===S.bookId&&await h(S.bookId),R}}catch(R){throw console.error("Failed to create chapter:",R),R}}async function C(S,w){var R,z;try{if(window.electronAPI){const A=await window.electronAPI.chapter.update(S,w),D=t.value.findIndex(W=>W.id===S);return D!==-1&&(t.value[D]=A),((R=s.value)==null?void 0:R.id)===S&&(s.value=A),((z=n.value)==null?void 0:z.id)===A.bookId&&await h(A.bookId),A}}catch(A){throw console.error("Failed to update chapter:",A),A}}async function I(S){var w,R;try{const z=t.value.find(A=>A.id===S);if(!z)return;window.electronAPI&&(await window.electronAPI.chapter.delete(S),t.value=t.value.filter(A=>A.id!==S),((w=s.value)==null?void 0:w.id)===S&&(s.value=null),((R=n.value)==null?void 0:R.id)===z.bookId&&await h(z.bookId))}catch(z){throw console.error("Failed to delete chapter:",z),z}}async function B(S){var w;try{if(window.electronAPI){const R=await window.electronAPI.chapter.getById(S);s.value=R,R&&R.bookId!==((w=n.value)==null?void 0:w.id)&&await h(R.bookId)}}catch(R){throw console.error("Failed to set current chapter:",R),R}}function N(S){if(!S.trim())return e.value;const w=S.toLowerCase();return e.value.filter(R=>R.title.toLowerCase().includes(w)||R.author.toLowerCase().includes(w)||R.description&&R.description.toLowerCase().includes(w))}function F(){n.value=null,s.value=null}return{books:e,chapters:t,currentBook:n,currentChapter:s,isLoading:r,currentBookChapters:o,totalWordCount:i,loadBooks:l,createBook:c,updateBook:a,deleteBook:u,setCurrentBook:h,loadChapters:g,createChapter:m,updateChapter:C,deleteChapter:I,setCurrentChapter:B,searchBooks:N,clearCurrentSelection:F}}),af={class:"title-bar"},df={class:"title-bar-content"},hf={class:"title-bar-left"},pf={key:0,class:"book-title"},gf={class:"title-bar-center"},mf={key:0,class:"breadcrumb"},yf={key:0,class:"breadcrumb-separator"},vf={class:"title-bar-right"},_f=["title"],bf={key:0,width:"12",height:"12",viewBox:"0 0 12 12"},wf={key:1,width:"12",height:"12",viewBox:"0 0 12 12"},Sf=Dn({__name:"TitleBar",setup(e){const t=lf(),n=ff(),s=Ie(!1),r=ce(()=>{var a;return((a=n.currentBook)==null?void 0:a.title)||""}),o=ce(()=>{const a=[];return t.name==="Library"?a.push("书架"):t.name==="Editor"?(a.push("编辑器"),n.currentChapter&&a.push(n.currentChapter.title)):t.name==="Settings"&&a.push("设置"),a});async function i(){window.electronAPI&&await window.electronAPI.window.minimize()}async function l(){window.electronAPI&&(await window.electronAPI.window.maximize(),s.value=!s.value)}async function c(){window.electronAPI&&await window.electronAPI.window.close()}return Bs(()=>{}),(a,u)=>(ke(),Qe("div",af,[ie("div",df,[ie("div",hf,[u[0]||(u[0]=ie("div",{class:"app-icon"},[ie("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor"},[ie("path",{d:"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"})])],-1)),u[1]||(u[1]=ie("span",{class:"app-title"},"Novel Writer",-1)),r.value?(ke(),Qe("span",pf,"- "+fs(r.value),1)):wn("",!0)]),ie("div",gf,[o.value.length>0?(ke(),Qe("div",mf,[(ke(!0),Qe(Ve,null,Ml(o.value,(h,g)=>(ke(),Qe("span",{key:g,class:"breadcrumb-item"},[Xo(fs(h)+" ",1),g<o.value.length-1?(ke(),Qe("span",yf,"/")):wn("",!0)]))),128))])):wn("",!0)]),ie("div",vf,[ie("button",{class:"title-bar-button",onClick:i,title:"最小化"},u[2]||(u[2]=[ie("svg",{width:"12",height:"12",viewBox:"0 0 12 12"},[ie("rect",{x:"2",y:"5",width:"8",height:"2",fill:"currentColor"})],-1)])),ie("button",{class:"title-bar-button",onClick:l,title:s.value?"还原":"最大化"},[s.value?(ke(),Qe("svg",wf,u[4]||(u[4]=[ie("rect",{x:"2",y:"3",width:"6",height:"6",stroke:"currentColor","stroke-width":"1",fill:"none"},null,-1),ie("rect",{x:"4",y:"1",width:"6",height:"6",stroke:"currentColor","stroke-width":"1",fill:"none"},null,-1)]))):(ke(),Qe("svg",bf,u[3]||(u[3]=[ie("rect",{x:"2",y:"2",width:"8",height:"8",stroke:"currentColor","stroke-width":"1",fill:"none"},null,-1)])))],8,_f),ie("button",{class:"title-bar-button close-button",onClick:c,title:"关闭"},u[5]||(u[5]=[ie("svg",{width:"12",height:"12",viewBox:"0 0 12 12"},[ie("path",{d:"M1 1L11 11M11 1L1 11",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round"})],-1)]))])])]))}}),xf=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},Ef=xf(Sf,[["__scopeId","data-v-40016753"]]),Cf={class:"main-content"},Pf=Dn({__name:"App",setup(e){const t=of(),n=uf(),s=ce(()=>`theme-${n.settings.editor.theme}`),r=ce(()=>n.isFullscreen);Bs(async()=>{await n.loadSettings(),o()});function o(){if(!window.electronAPI)return;const i=[window.electronAPI.menu.onNewBook(()=>{console.log("New book requested")}),window.electronAPI.menu.onViewLibrary(()=>{t.push("/library")}),window.electronAPI.menu.onViewEditor(()=>{t.push("/editor")}),window.electronAPI.menu.onToggleFocusMode(()=>{n.toggleFocusMode()}),window.electronAPI.menu.onSettings(()=>{t.push("/settings")})];Vs(()=>{i.forEach(l=>l())})}return(i,l)=>{const c=Il("router-view");return ke(),Qe("div",{id:"app",class:Nn(s.value)},[r.value?wn("",!0):(ke(),Jo(Ef,{key:0})),ie("main",Cf,[Se(c)])],2)}}}),Rf="modulepreload",Af=function(e,t){return new URL(e,t).href},Qr={},cs=function(t,n,s){let r=Promise.resolve();if(n&&n.length>0){const i=document.getElementsByTagName("link"),l=document.querySelector("meta[property=csp-nonce]"),c=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));r=Promise.allSettled(n.map(a=>{if(a=Af(a,s),a in Qr)return;Qr[a]=!0;const u=a.endsWith(".css"),h=u?'[rel="stylesheet"]':"";if(!!s)for(let C=i.length-1;C>=0;C--){const I=i[C];if(I.href===a&&(!u||I.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${a}"]${h}`))return;const m=document.createElement("link");if(m.rel=u?"stylesheet":Rf,u||(m.as="script"),m.crossOrigin="",m.href=a,c&&m.setAttribute("nonce",c),document.head.appendChild(m),u)return new Promise((C,I)=>{m.addEventListener("load",C),m.addEventListener("error",()=>I(new Error(`Unable to preload CSS for ${a}`)))})}))}function o(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return r.then(i=>{for(const l of i||[])l.status==="rejected"&&o(l.reason);return t().catch(o)})},If=[{path:"/",redirect:"/library"},{path:"/library",name:"Library",component:()=>cs(()=>import("./Library-OibGlRi8.js"),__vite__mapDeps([0,1]),import.meta.url),meta:{title:"书架"}},{path:"/editor/:bookId?/:chapterId?",name:"Editor",component:()=>cs(()=>import("./Editor-sV1BZILL.js"),__vite__mapDeps([2,3]),import.meta.url),meta:{title:"编辑器"}},{path:"/settings",name:"Settings",component:()=>cs(()=>import("./Settings-C5O1G57G.js"),__vite__mapDeps([4,5]),import.meta.url),meta:{title:"设置"}}],Tf=sf({history:Mu(),routes:If}),Of=Yc(),Gs=zc(Pf);Gs.use(Of);Gs.use(Tf);Gs.mount("#app");export{Vs as A,lf as B,Ve as F,xf as _,ie as a,wn as b,Qe as c,Dn as d,ce as e,Bs as f,$n as g,Mf as h,Lf as i,Jo as j,Xo as k,Ml as l,of as m,Nn as n,ke as o,uf as p,Ot as q,Ie as r,Ff as s,fs as t,ff as u,kf as v,Nf as w,Is as x,Hf as y,Yt as z};
