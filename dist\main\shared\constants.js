"use strict";
// 应用常量
Object.defineProperty(exports, "__esModule", { value: true });
exports.EXPORT_FORMATS = exports.FONT_FAMILIES = exports.THEMES = exports.DEFAULT_APP_SETTINGS = exports.DEFAULT_EDITOR_SETTINGS = exports.APP_VERSION = exports.APP_NAME = void 0;
exports.APP_NAME = 'Novel Writer';
exports.APP_VERSION = '1.0.0';
// 默认设置
exports.DEFAULT_EDITOR_SETTINGS = {
    fontSize: 16,
    fontFamily: 'Microsoft YaHei, SimSun, serif',
    lineHeight: 1.8,
    theme: 'light',
    backgroundColor: '#ffffff',
    textColor: '#333333',
    focusMode: false,
    typewriterMode: false,
    zenMode: false
};
exports.DEFAULT_APP_SETTINGS = {
    editor: exports.DEFAULT_EDITOR_SETTINGS,
    autoSave: true,
    autoSaveInterval: 30000, // 30秒
    language: 'zh-CN',
    windowSize: {
        width: 1200,
        height: 800
    }
};
// 主题配置
exports.THEMES = {
    light: {
        name: '明亮',
        backgroundColor: '#ffffff',
        textColor: '#333333',
        secondaryColor: '#666666',
        borderColor: '#e0e0e0',
        primaryColor: '#007acc'
    },
    dark: {
        name: '暗黑',
        backgroundColor: '#1e1e1e',
        textColor: '#d4d4d4',
        secondaryColor: '#969696',
        borderColor: '#404040',
        primaryColor: '#0078d4'
    },
    sepia: {
        name: '护眼',
        backgroundColor: '#f7f3e9',
        textColor: '#5c4b37',
        secondaryColor: '#8b7355',
        borderColor: '#d3c7b8',
        primaryColor: '#8b4513'
    }
};
// 字体选项
exports.FONT_FAMILIES = [
    { name: '微软雅黑', value: 'Microsoft YaHei, sans-serif' },
    { name: '宋体', value: 'SimSun, serif' },
    { name: '黑体', value: 'SimHei, sans-serif' },
    { name: '楷体', value: 'KaiTi, serif' },
    { name: 'Times New Roman', value: 'Times New Roman, serif' },
    { name: 'Arial', value: 'Arial, sans-serif' },
    { name: 'Helvetica', value: 'Helvetica, sans-serif' }
];
// 文件格式
exports.EXPORT_FORMATS = [
    { name: 'TXT 文本', value: 'txt', extension: '.txt' },
    { name: 'Word 文档', value: 'docx', extension: '.docx' },
    { name: 'PDF 文档', value: 'pdf', extension: '.pdf' }
];
