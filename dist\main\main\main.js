"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const dataManager_1 = require("./services/dataManager");
const windowManager_1 = require("./services/windowManager");
const menuManager_1 = require("./services/menuManager");
const ipcManager_1 = require("./services/ipcManager");
class Application {
    constructor() {
        this.dataManager = new dataManager_1.DataManager();
        this.windowManager = new windowManager_1.WindowManager();
        this.menuManager = new menuManager_1.MenuManager();
        this.ipcManager = new ipcManager_1.IpcManager(this.dataManager);
    }
    async initialize() {
        // 确保只有一个实例运行
        const gotTheLock = electron_1.app.requestSingleInstanceLock();
        if (!gotTheLock) {
            electron_1.app.quit();
            return;
        }
        // 设置应用程序用户模型ID (Windows)
        if (process.platform === 'win32') {
            electron_1.app.setAppUserModelId('com.novelwriter.app');
        }
        // 应用程序事件监听
        electron_1.app.whenReady().then(() => this.onReady());
        electron_1.app.on('window-all-closed', this.onWindowAllClosed);
        electron_1.app.on('activate', this.onActivate);
        electron_1.app.on('second-instance', this.onSecondInstance);
        // 初始化数据管理器
        await this.dataManager.initialize();
        // 注册IPC处理器
        this.ipcManager.registerHandlers();
    }
    async onReady() {
        // 创建主窗口
        const mainWindow = await this.windowManager.createMainWindow();
        // 设置菜单
        const menu = this.menuManager.createMenu(mainWindow);
        electron_1.Menu.setApplicationMenu(menu);
        // 开发模式下打开开发者工具
        if (process.argv.includes('--dev')) {
            mainWindow.webContents.openDevTools();
        }
    }
    onWindowAllClosed() {
        // 在 macOS 上，除非用户用 Cmd + Q 确定地退出，
        // 否则绝大部分应用及其菜单栏会保持激活。
        if (process.platform !== 'darwin') {
            electron_1.app.quit();
        }
    }
    async onActivate() {
        // 在 macOS 上，当点击 dock 图标并且没有其他窗口打开的时候，
        // 通常在应用程序中重新创建一个窗口。
        if (electron_1.BrowserWindow.getAllWindows().length === 0) {
            await this.windowManager.createMainWindow();
        }
    }
    onSecondInstance() {
        // 当运行第二个实例时，将会聚焦到主窗口
        const mainWindow = this.windowManager.getMainWindow();
        if (mainWindow) {
            if (mainWindow.isMinimized())
                mainWindow.restore();
            mainWindow.focus();
        }
    }
}
// 创建应用程序实例并初始化
const application = new Application();
application.initialize().catch(console.error);
