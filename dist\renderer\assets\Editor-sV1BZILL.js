import{d as T,u as Q,r as y,e as M,f as U,g as X,c as h,a as t,w as V,h as k,v as H,o as u,_ as D,t as w,p as Y,F as N,l as q,i as ct,q as ft,s as Z,x as O,n as A,y as gt,z as ht,A as mt,b as S,j as E,k as Ct,B as bt,m as yt}from"./main-CHDPsXFQ.js";const wt={class:"dialog-header"},kt={class:"form-group"},$t={class:"form-group"},Mt=["placeholder"],Lt={class:"form-group"},xt={class:"dialog-actions"},St=["disabled"],Ht=T({__name:"NewChapterDialog",props:{bookId:{}},emits:["close","create"],setup(z,{emit:m}){const a=z,c=m,f=Q(),b=y(),d=y({title:"",order:null,content:""}),$=M(()=>d.value.title.trim()),v=M(()=>{const r=f.currentBookChapters;return r.length>0?Math.max(...r.map(n=>n.order))+1:1});function i(){if(!$.value)return;const r={bookId:a.bookId,title:d.value.title.trim(),content:d.value.content.trim(),order:d.value.order||v.value,wordCount:d.value.content.trim().length};c("create",r)}return U(async()=>{var r;await X(),(r=b.value)==null||r.focus()}),(r,n)=>(u(),h("div",{class:"dialog-overlay",onClick:n[6]||(n[6]=C=>r.$emit("close"))},[t("div",{class:"dialog",onClick:n[5]||(n[5]=V(()=>{},["stop"]))},[t("div",wt,[n[8]||(n[8]=t("h2",null,"新建章节",-1)),t("button",{class:"close-btn",onClick:n[0]||(n[0]=C=>r.$emit("close"))},n[7]||(n[7]=[t("svg",{width:"20",height:"20",viewBox:"0 0 24 24"},[t("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z",fill:"currentColor"})],-1)]))]),t("form",{onSubmit:V(i,["prevent"]),class:"dialog-content"},[t("div",kt,[n[9]||(n[9]=t("label",{for:"title"},"章节标题 *",-1)),k(t("input",{id:"title","onUpdate:modelValue":n[1]||(n[1]=C=>d.value.title=C),type:"text",placeholder:"请输入章节标题",required:"",maxlength:"100",ref_key:"titleInput",ref:b},null,512),[[H,d.value.title]])]),t("div",$t,[n[10]||(n[10]=t("label",{for:"order"},"章节序号",-1)),k(t("input",{id:"order","onUpdate:modelValue":n[2]||(n[2]=C=>d.value.order=C),type:"number",min:"1",placeholder:`建议序号: ${v.value}`},null,8,Mt),[[H,d.value.order,void 0,{number:!0}]]),n[11]||(n[11]=t("div",{class:"form-hint"},"留空将自动分配序号",-1))]),t("div",Lt,[n[12]||(n[12]=t("label",{for:"content"},"章节内容",-1)),k(t("textarea",{id:"content","onUpdate:modelValue":n[3]||(n[3]=C=>d.value.content=C),placeholder:"请输入章节内容（可选）",rows:"6"},null,512),[[H,d.value.content]])]),t("div",xt,[t("button",{type:"button",class:"btn btn-secondary",onClick:n[4]||(n[4]=C=>r.$emit("close"))}," 取消 "),t("button",{type:"submit",class:"btn btn-primary",disabled:!$.value}," 创建 ",8,St)])],32)])]))}}),Vt=D(Ht,[["__scopeId","data-v-06ccfc29"]]),zt={class:"dialog-header"},Ft={class:"form-group"},Bt={class:"form-group"},_t={class:"chapter-stats"},It={class:"stat-item"},At={class:"stat-value"},Tt={class:"stat-item"},Ut={class:"stat-value"},Dt={class:"stat-item"},Zt={class:"stat-value"},Et={class:"dialog-actions"},Nt=["disabled"],qt=T({__name:"EditChapterDialog",props:{chapter:{}},emits:["close","update"],setup(z,{emit:m}){const a=z,c=m,f=y({title:"",order:1}),b=M(()=>f.value.title.trim());function d(){if(!b.value)return;const v={title:f.value.title.trim(),order:f.value.order};c("update",v)}function $(v){return new Date(v).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}return U(()=>{f.value={title:a.chapter.title,order:a.chapter.order}}),(v,i)=>(u(),h("div",{class:"dialog-overlay",onClick:i[5]||(i[5]=r=>v.$emit("close"))},[t("div",{class:"dialog",onClick:i[4]||(i[4]=V(()=>{},["stop"]))},[t("div",zt,[i[7]||(i[7]=t("h2",null,"编辑章节",-1)),t("button",{class:"close-btn",onClick:i[0]||(i[0]=r=>v.$emit("close"))},i[6]||(i[6]=[t("svg",{width:"20",height:"20",viewBox:"0 0 24 24"},[t("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z",fill:"currentColor"})],-1)]))]),t("form",{onSubmit:V(d,["prevent"]),class:"dialog-content"},[t("div",Ft,[i[8]||(i[8]=t("label",{for:"title"},"章节标题 *",-1)),k(t("input",{id:"title","onUpdate:modelValue":i[1]||(i[1]=r=>f.value.title=r),type:"text",placeholder:"请输入章节标题",required:"",maxlength:"100"},null,512),[[H,f.value.title]])]),t("div",Bt,[i[9]||(i[9]=t("label",{for:"order"},"章节序号",-1)),k(t("input",{id:"order","onUpdate:modelValue":i[2]||(i[2]=r=>f.value.order=r),type:"number",min:"1"},null,512),[[H,f.value.order,void 0,{number:!0}]])]),t("div",_t,[t("div",It,[i[10]||(i[10]=t("span",{class:"stat-label"},"字数",-1)),t("span",At,w(v.chapter.wordCount),1)]),t("div",Tt,[i[11]||(i[11]=t("span",{class:"stat-label"},"创建时间",-1)),t("span",Ut,w($(v.chapter.createdAt)),1)]),t("div",Dt,[i[12]||(i[12]=t("span",{class:"stat-label"},"更新时间",-1)),t("span",Zt,w($(v.chapter.updatedAt)),1)])]),t("div",Et,[t("button",{type:"button",class:"btn btn-secondary",onClick:i[3]||(i[3]=r=>v.$emit("close"))}," 取消 "),t("button",{type:"submit",class:"btn btn-primary",disabled:!b.value}," 保存 ",8,Nt)])],32)])]))}}),Ot=D(qt,[["__scopeId","data-v-6a7ec683"]]),Rt={class:"settings-header"},jt={class:"settings-content"},Pt={class:"setting-group"},Wt={class:"theme-options"},Gt=["onClick"],Jt={class:"setting-group"},Kt={class:"setting-row"},Qt=["value"],Xt={class:"setting-row"},Yt={class:"slider-group"},te={class:"slider-value"},ee={class:"setting-row"},oe={class:"slider-group"},le={class:"slider-value"},se={class:"setting-group"},ne={class:"setting-row"},ie={class:"toggle-switch"},ae={class:"setting-row"},re={class:"toggle-switch"},de={class:"setting-row"},ue={class:"toggle-switch"},ve={class:"setting-group"},pe=T({__name:"EditorSettings",emits:["close"],setup(z){const m=Y(),a=y({fontFamily:"",fontSize:16,lineHeight:1.8,zenMode:!1,typewriterMode:!1,focusMode:!1}),c=M(()=>m.currentTheme),f=[{name:"明亮",value:"light",preview:{background:"#ffffff",border:"2px solid #e0e0e0"}},{name:"暗黑",value:"dark",preview:{background:"#1e1e1e",border:"2px solid #404040"}},{name:"护眼",value:"sepia",preview:{background:"#f7f3e9",border:"2px solid #d3c7b8"}}],b=gt,d=M(()=>({fontFamily:a.value.fontFamily,fontSize:`${a.value.fontSize}px`,lineHeight:a.value.lineHeight}));async function $(g){await m.setTheme(g)}async function v(){await m.setFontFamily(a.value.fontFamily)}async function i(){await m.setFontSize(a.value.fontSize)}async function r(){await m.setLineHeight(a.value.lineHeight)}async function n(){await m.updateEditorSettings({zenMode:a.value.zenMode})}async function C(){await m.updateEditorSettings({typewriterMode:a.value.typewriterMode})}async function B(){await m.updateEditorSettings({focusMode:a.value.focusMode})}return U(()=>{const g=m.editorSettings;a.value={fontFamily:g.fontFamily,fontSize:g.fontSize,lineHeight:g.lineHeight,zenMode:g.zenMode,typewriterMode:g.typewriterMode,focusMode:g.focusMode}}),(g,o)=>(u(),h("div",{class:"settings-overlay",onClick:o[8]||(o[8]=l=>g.$emit("close"))},[t("div",{class:"settings-panel",onClick:o[7]||(o[7]=V(()=>{},["stop"]))},[t("div",Rt,[o[10]||(o[10]=t("h2",null,"编辑器设置",-1)),t("button",{class:"close-btn",onClick:o[0]||(o[0]=l=>g.$emit("close"))},o[9]||(o[9]=[t("svg",{width:"20",height:"20",viewBox:"0 0 24 24"},[t("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z",fill:"currentColor"})],-1)]))]),t("div",jt,[t("div",Pt,[o[11]||(o[11]=t("h3",null,"主题",-1)),t("div",Wt,[(u(),h(N,null,q(f,l=>t("button",{key:l.value,class:A(["theme-option",{active:c.value===l.value}]),onClick:L=>$(l.value)},[t("div",{class:"theme-preview",style:O(l.preview)},null,4),t("span",null,w(l.name),1)],10,Gt)),64))])]),t("div",Jt,[o[15]||(o[15]=t("h3",null,"字体",-1)),t("div",Kt,[o[12]||(o[12]=t("label",null,"字体族",-1)),k(t("select",{"onUpdate:modelValue":o[1]||(o[1]=l=>a.value.fontFamily=l),onChange:v},[(u(!0),h(N,null,q(ft(b),l=>(u(),h("option",{key:l.value,value:l.value},w(l.name),9,Qt))),128))],544),[[ct,a.value.fontFamily]])]),t("div",Xt,[o[13]||(o[13]=t("label",null,"字体大小",-1)),t("div",Yt,[k(t("input",{type:"range",min:"12",max:"24","onUpdate:modelValue":o[2]||(o[2]=l=>a.value.fontSize=l),onInput:i},null,544),[[H,a.value.fontSize,void 0,{number:!0}]]),t("span",te,w(a.value.fontSize)+"px",1)])]),t("div",ee,[o[14]||(o[14]=t("label",null,"行高",-1)),t("div",oe,[k(t("input",{type:"range",min:"1.2",max:"2.5",step:"0.1","onUpdate:modelValue":o[3]||(o[3]=l=>a.value.lineHeight=l),onInput:r},null,544),[[H,a.value.lineHeight,void 0,{number:!0}]]),t("span",le,w(a.value.lineHeight),1)])])]),t("div",se,[o[22]||(o[22]=t("h3",null,"编辑模式",-1)),t("div",ne,[o[17]||(o[17]=t("label",null,"心流模式",-1)),t("div",ie,[k(t("input",{type:"checkbox",id:"zenMode","onUpdate:modelValue":o[4]||(o[4]=l=>a.value.zenMode=l),onChange:n},null,544),[[Z,a.value.zenMode]]),o[16]||(o[16]=t("label",{for:"zenMode",class:"toggle-label"},null,-1))])]),t("div",ae,[o[19]||(o[19]=t("label",null,"打字机模式",-1)),t("div",re,[k(t("input",{type:"checkbox",id:"typewriterMode","onUpdate:modelValue":o[5]||(o[5]=l=>a.value.typewriterMode=l),onChange:C},null,544),[[Z,a.value.typewriterMode]]),o[18]||(o[18]=t("label",{for:"typewriterMode",class:"toggle-label"},null,-1))])]),t("div",de,[o[21]||(o[21]=t("label",null,"专注模式",-1)),t("div",ue,[k(t("input",{type:"checkbox",id:"focusMode","onUpdate:modelValue":o[6]||(o[6]=l=>a.value.focusMode=l),onChange:B},null,544),[[Z,a.value.focusMode]]),o[20]||(o[20]=t("label",{for:"focusMode",class:"toggle-label"},null,-1))])])]),t("div",ve,[o[24]||(o[24]=t("h3",null,"预览",-1)),t("div",{class:"preview-area",style:O(d.value)},o[23]||(o[23]=[t("p",null,"这是一段示例文本，用于预览当前的字体和样式设置。你可以在这里看到字体大小、行高和颜色的效果。",-1)]),4)])])])]))}}),ce=D(pe,[["__scopeId","data-v-3787a73a"]]),fe={class:"sidebar-header"},ge=["title"],he={key:0,class:"book-info"},me={class:"book-title"},Ce={class:"book-stats"},be={key:0,class:"sidebar-content"},ye={class:"chapter-list"},we={class:"chapter-list-header"},ke={class:"chapters"},$e=["onClick"],Me={class:"chapter-info"},Le={class:"chapter-order"},xe={class:"chapter-title"},Se={class:"chapter-actions"},He=["onClick"],Ve=["onClick"],ze={class:"main-editor"},Fe={key:0,class:"editor-toolbar"},Be={class:"toolbar-left"},_e={key:0,class:"chapter-nav"},Ie=["disabled"],Ae={class:"chapter-title"},Te=["disabled"],Ue={class:"toolbar-right"},De={class:"word-count"},Ze={key:0,class:"no-chapter"},Ee={key:1,class:"chapter-editor"},Ne={key:0,class:"chapter-header"},qe=["placeholder"],Oe=T({__name:"Editor",setup(z){const m=bt(),a=yt(),c=Q(),f=Y(),b=y(!1),d=y(!1),$=y(!1),v=y(!1),i=y(null),r=y(""),n=y(""),C=y(0),B=y(),g=y(),o=M(()=>c.currentBook),l=M(()=>c.currentChapter),L=M(()=>c.currentBookChapters),F=M(()=>f.editorSettings.zenMode),R=M(()=>l.value?L.value.findIndex(s=>s.id===l.value.id)>0:!1),j=M(()=>l.value?L.value.findIndex(e=>e.id===l.value.id)<L.value.length-1:!1),tt=M(()=>{const s=f.editorSettings;return{fontSize:`${s.fontSize}px`,fontFamily:s.fontFamily,lineHeight:s.lineHeight,backgroundColor:s.backgroundColor,color:s.textColor}});async function et(){const s=m.params.bookId,e=m.params.chapterId;s&&(await c.setCurrentBook(s),e&&await c.setCurrentChapter(e))}function ot(){a.push("/library")}function lt(){f.toggleZenMode()}async function _(s){var e;await c.setCurrentChapter(s),a.push(`/editor/${(e=o.value)==null?void 0:e.id}/${s}`)}async function st(){if(!R.value)return;const s=L.value.findIndex(x=>x.id===l.value.id),e=L.value[s-1];await _(e.id)}async function nt(){if(!j.value)return;const s=L.value.findIndex(x=>x.id===l.value.id),e=L.value[s+1];await _(e.id)}async function it(s){try{const e=await c.createChapter(s);d.value=!1,e&&await _(e.id)}catch(e){console.error("Failed to create chapter:",e)}}function at(s){i.value=s,$.value=!0}async function rt(s){if(i.value)try{await c.updateChapter(i.value.id,s),$.value=!1,i.value=null}catch(e){console.error("Failed to update chapter:",e)}}async function dt(s){var e,x;if(confirm(`确定要删除章节"${s.title}"吗？`))try{await c.deleteChapter(s.id),((e=l.value)==null?void 0:e.id)===s.id&&a.push(`/editor/${(x=o.value)==null?void 0:x.id}`)}catch(I){console.error("Failed to delete chapter:",I)}}function ut(){C.value=n.value.length,g.value&&clearTimeout(g.value),g.value=setTimeout(()=>{P()},2e3)}async function vt(){if(!(!l.value||r.value===l.value.title))try{await c.updateChapter(l.value.id,{title:r.value})}catch(s){console.error("Failed to save chapter title:",s)}}async function P(){if(!(!l.value||n.value===l.value.content))try{await c.updateChapter(l.value.id,{content:n.value,wordCount:n.value.length})}catch(s){console.error("Failed to save chapter content:",s)}}function pt(){f.editorSettings.typewriterMode}return ht(l,s=>{s?(r.value=s.title,n.value=s.content,C.value=s.content.length):(r.value="",n.value="",C.value=0)},{immediate:!0}),U(async()=>{var s;await et(),await X(),(s=B.value)==null||s.focus()}),mt(()=>{g.value&&clearTimeout(g.value),P()}),(s,e)=>{var x,I,W,G;return u(),h("div",{class:A(["editor-layout",{"zen-mode":F.value}])},[F.value?S("",!0):(u(),h("div",{key:0,class:A(["sidebar",{collapsed:b.value}])},[t("div",fe,[t("button",{class:"sidebar-toggle",onClick:e[0]||(e[0]=p=>b.value=!b.value),title:b.value?"展开侧边栏":"收起侧边栏"},e[9]||(e[9]=[t("svg",{width:"16",height:"16",viewBox:"0 0 24 24"},[t("path",{d:"M3,6H21V8H3V6M3,11H21V13H3V11M3,16H21V18H3V16Z",fill:"currentColor"})],-1)]),8,ge),b.value?S("",!0):(u(),h("div",he,[t("h3",me,w(((x=o.value)==null?void 0:x.title)||"未选择书籍"),1),t("div",Ce,[t("span",null,w(((I=o.value)==null?void 0:I.wordCount)||0)+" 字",1),t("span",null,w(((W=o.value)==null?void 0:W.chapterCount)||0)+" 章",1)])]))]),b.value?S("",!0):(u(),h("div",be,[t("div",ye,[t("div",we,[e[11]||(e[11]=t("h4",null,"章节列表",-1)),t("button",{class:"add-chapter-btn",onClick:e[1]||(e[1]=p=>d.value=!0)},e[10]||(e[10]=[t("svg",{width:"14",height:"14",viewBox:"0 0 24 24"},[t("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z",fill:"currentColor"})],-1)]))]),t("div",ke,[(u(!0),h(N,null,q(L.value,p=>{var J;return u(),h("div",{key:p.id,class:A(["chapter-item",{active:((J=l.value)==null?void 0:J.id)===p.id}]),onClick:K=>_(p.id)},[t("div",Me,[t("span",Le,w(p.order),1),t("span",xe,w(p.title),1)]),t("div",Se,[t("button",{onClick:V(K=>at(p),["stop"]),title:"编辑"},e[12]||(e[12]=[t("svg",{width:"12",height:"12",viewBox:"0 0 24 24"},[t("path",{d:"M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z",fill:"currentColor"})],-1)]),8,He),t("button",{onClick:V(K=>dt(p),["stop"]),title:"删除"},e[13]||(e[13]=[t("svg",{width:"12",height:"12",viewBox:"0 0 24 24"},[t("path",{d:"M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z",fill:"currentColor"})],-1)]),8,Ve)])],10,$e)}),128))])])]))],2)),t("div",ze,[F.value?S("",!0):(u(),h("div",Fe,[t("div",Be,[t("button",{onClick:ot,class:"back-btn",title:"返回书架"},e[14]||(e[14]=[t("svg",{width:"16",height:"16",viewBox:"0 0 24 24"},[t("path",{d:"M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z",fill:"currentColor"})],-1),Ct(" 返回书架 ",-1)])),l.value?(u(),h("div",_e,[t("button",{onClick:st,disabled:!R.value,title:"上一章"},e[15]||(e[15]=[t("svg",{width:"16",height:"16",viewBox:"0 0 24 24"},[t("path",{d:"M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z",fill:"currentColor"})],-1)]),8,Ie),t("span",Ae,w(l.value.title),1),t("button",{onClick:nt,disabled:!j.value,title:"下一章"},e[16]||(e[16]=[t("svg",{width:"16",height:"16",viewBox:"0 0 24 24"},[t("path",{d:"M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z",fill:"currentColor"})],-1)]),8,Te)])):S("",!0)]),t("div",Ue,[t("div",De," 字数: "+w(C.value),1),t("button",{onClick:lt,class:"zen-btn",title:"心流模式"},e[17]||(e[17]=[t("svg",{width:"16",height:"16",viewBox:"0 0 24 24"},[t("path",{d:"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z",fill:"currentColor"})],-1)])),t("button",{onClick:e[2]||(e[2]=p=>v.value=!0),class:"settings-btn",title:"编辑器设置"},e[18]||(e[18]=[t("svg",{width:"16",height:"16",viewBox:"0 0 24 24"},[t("path",{d:"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z",fill:"currentColor"})],-1)]))])])),t("div",{class:"editor-content",style:O(tt.value)},[l.value?(u(),h("div",Ee,[F.value?S("",!0):(u(),h("div",Ne,[k(t("input",{"onUpdate:modelValue":e[4]||(e[4]=p=>r.value=p),class:"chapter-title-input",placeholder:"章节标题",onBlur:vt},null,544),[[H,r.value]])])),k(t("textarea",{ref_key:"editorTextarea",ref:B,"onUpdate:modelValue":e[5]||(e[5]=p=>n.value=p),class:"editor-textarea",placeholder:F.value?"开始你的创作...":"在这里输入章节内容...",onInput:ut,onScroll:pt},null,40,qe),[[H,n.value]])])):(u(),h("div",Ze,[e[19]||(e[19]=t("svg",{width:"64",height:"64",viewBox:"0 0 24 24"},[t("path",{d:"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z",fill:"currentColor"})],-1)),e[20]||(e[20]=t("h3",null,"请选择一个章节开始编辑",-1)),e[21]||(e[21]=t("p",null,"从左侧章节列表中选择章节，或创建新章节",-1)),t("button",{class:"new-chapter-btn",onClick:e[3]||(e[3]=p=>d.value=!0)}," 新建章节 ")]))],4)]),d.value?(u(),E(Vt,{key:1,"book-id":((G=o.value)==null?void 0:G.id)||"",onClose:e[6]||(e[6]=p=>d.value=!1),onCreate:it},null,8,["book-id"])):S("",!0),$.value&&i.value?(u(),E(Ot,{key:2,chapter:i.value,onClose:e[7]||(e[7]=p=>$.value=!1),onUpdate:rt},null,8,["chapter"])):S("",!0),v.value?(u(),E(ce,{key:3,onClose:e[8]||(e[8]=p=>v.value=!1)})):S("",!0)],2)}}}),je=D(Oe,[["__scopeId","data-v-0e61954a"]]);export{je as default};
