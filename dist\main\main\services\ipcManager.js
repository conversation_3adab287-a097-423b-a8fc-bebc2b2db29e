"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpcManager = void 0;
const electron_1 = require("electron");
const fs_1 = require("fs");
const path_1 = require("path");
class IpcManager {
    constructor(dataManager) {
        this.dataManager = dataManager;
    }
    registerHandlers() {
        // 书籍管理
        electron_1.ipcMain.handle('book:create', async (_, bookData) => {
            return await this.dataManager.createBook(bookData);
        });
        electron_1.ipcMain.handle('book:update', async (_, id, updates) => {
            return await this.dataManager.updateBook(id, updates);
        });
        electron_1.ipcMain.handle('book:delete', async (_, id) => {
            return await this.dataManager.deleteBook(id);
        });
        electron_1.ipcMain.handle('book:getAll', async () => {
            return await this.dataManager.getAllBooks();
        });
        electron_1.ipcMain.handle('book:getById', async (_, id) => {
            return await this.dataManager.getBookById(id);
        });
        // 章节管理
        electron_1.ipcMain.handle('chapter:create', async (_, chapterData) => {
            return await this.dataManager.createChapter(chapterData);
        });
        electron_1.ipcMain.handle('chapter:update', async (_, id, updates) => {
            return await this.dataManager.updateChapter(id, updates);
        });
        electron_1.ipcMain.handle('chapter:delete', async (_, id) => {
            return await this.dataManager.deleteChapter(id);
        });
        electron_1.ipcMain.handle('chapter:getByBookId', async (_, bookId) => {
            return await this.dataManager.getChaptersByBookId(bookId);
        });
        electron_1.ipcMain.handle('chapter:getById', async (_, id) => {
            return await this.dataManager.getChapterById(id);
        });
        // 设置管理
        electron_1.ipcMain.handle('settings:get', async () => {
            return await this.dataManager.getSettings();
        });
        electron_1.ipcMain.handle('settings:update', async (_, settings) => {
            return await this.dataManager.updateSettings(settings);
        });
        // 文件操作
        electron_1.ipcMain.handle('file:export', async (_, bookId, format) => {
            return await this.exportBook(bookId, format);
        });
        electron_1.ipcMain.handle('file:import', async (_, filePath) => {
            return await this.importFile(filePath);
        });
        // 窗口控制
        electron_1.ipcMain.handle('window:minimize', (event) => {
            const window = event.sender.getOwnerBrowserWindow();
            window?.minimize();
        });
        electron_1.ipcMain.handle('window:maximize', (event) => {
            const window = event.sender.getOwnerBrowserWindow();
            if (window?.isMaximized()) {
                window.unmaximize();
            }
            else {
                window?.maximize();
            }
        });
        electron_1.ipcMain.handle('window:close', (event) => {
            const window = event.sender.getOwnerBrowserWindow();
            window?.close();
        });
        electron_1.ipcMain.handle('window:toggleFullscreen', (event) => {
            const window = event.sender.getOwnerBrowserWindow();
            if (window) {
                const isFullscreen = window.isFullScreen();
                window.setFullScreen(!isFullscreen);
            }
        });
        // 对话框
        electron_1.ipcMain.handle('dialog:showSaveDialog', async (event, options) => {
            const window = event.sender.getOwnerBrowserWindow();
            if (!window)
                return { canceled: true };
            return await electron_1.dialog.showSaveDialog(window, options);
        });
        electron_1.ipcMain.handle('dialog:showOpenDialog', async (event, options) => {
            const window = event.sender.getOwnerBrowserWindow();
            if (!window)
                return { canceled: true };
            return await electron_1.dialog.showOpenDialog(window, options);
        });
    }
    async exportBook(bookId, format) {
        try {
            const book = await this.dataManager.getBookById(bookId);
            if (!book) {
                throw new Error('书籍不存在');
            }
            const chapters = await this.dataManager.getChaptersByBookId(bookId);
            // 生成内容
            let content = `${book.title}\n`;
            content += `作者：${book.author}\n\n`;
            if (book.description) {
                content += `简介：\n${book.description}\n\n`;
            }
            content += '='.repeat(50) + '\n\n';
            for (const chapter of chapters) {
                content += `第${chapter.order}章 ${chapter.title}\n\n`;
                content += chapter.content + '\n\n';
            }
            // 根据格式处理
            switch (format) {
                case 'txt':
                    return await this.exportAsTxt(book.title, content);
                case 'docx':
                    return await this.exportAsDocx(book.title, content);
                case 'pdf':
                    return await this.exportAsPdf(book.title, content);
                default:
                    throw new Error('不支持的导出格式');
            }
        }
        catch (error) {
            throw new Error(`导出失败: ${error.message}`);
        }
    }
    async exportAsTxt(title, content) {
        const result = await electron_1.dialog.showSaveDialog({
            title: '导出为 TXT',
            defaultPath: `${title}.txt`,
            filters: [
                { name: 'Text Files', extensions: ['txt'] }
            ]
        });
        if (result.canceled || !result.filePath) {
            throw new Error('用户取消导出');
        }
        await fs_1.promises.writeFile(result.filePath, content, 'utf8');
        return result.filePath;
    }
    async exportAsDocx(title, content) {
        // 这里需要实现 Word 文档导出
        // 可以使用 docx 库或其他方案
        throw new Error('Word 导出功能待实现');
    }
    async exportAsPdf(title, content) {
        // 这里需要实现 PDF 导出
        // 可以使用 puppeteer 或其他方案
        throw new Error('PDF 导出功能待实现');
    }
    async importFile(filePath) {
        try {
            const ext = (0, path_1.extname)(filePath).toLowerCase();
            if (ext !== '.txt') {
                throw new Error('目前只支持导入 TXT 文件');
            }
            const content = await fs_1.promises.readFile(filePath, 'utf8');
            const fileName = (0, path_1.join)(filePath).split(/[/\\]/).pop() || 'untitled';
            const title = fileName.replace(/\.[^/.]+$/, '');
            return { title, content };
        }
        catch (error) {
            throw new Error(`导入失败: ${error.message}`);
        }
    }
}
exports.IpcManager = IpcManager;
