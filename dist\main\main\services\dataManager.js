"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataManager = void 0;
const electron_store_1 = __importDefault(require("electron-store"));
const uuid_1 = require("uuid");
const constants_1 = require("../../shared/constants");
class DataManager {
    constructor() {
        // 初始化数据存储
        this.store = new electron_store_1.default();
        this.booksStore = new electron_store_1.default({
            name: 'books',
            defaults: { books: [] }
        });
        this.chaptersStore = new electron_store_1.default({
            name: 'chapters',
            defaults: { chapters: [] }
        });
        this.settingsStore = new electron_store_1.default({
            name: 'settings',
            defaults: constants_1.DEFAULT_APP_SETTINGS
        });
    }
    async initialize() {
        // 数据迁移和初始化逻辑
        console.log('DataManager initialized');
    }
    // 书籍管理
    async createBook(bookData) {
        const book = {
            ...bookData,
            id: (0, uuid_1.v4)(),
            createdAt: new Date(),
            updatedAt: new Date()
        };
        const books = this.booksStore.get('books');
        books.push(book);
        this.booksStore.set('books', books);
        return book;
    }
    async updateBook(id, updates) {
        const books = this.booksStore.get('books');
        const index = books.findIndex((book) => book.id === id);
        if (index === -1) {
            throw new Error(`Book with id ${id} not found`);
        }
        const updatedBook = {
            ...books[index],
            ...updates,
            updatedAt: new Date()
        };
        books[index] = updatedBook;
        this.booksStore.set('books', books);
        return updatedBook;
    }
    async deleteBook(id) {
        const books = this.booksStore.get('books');
        const filteredBooks = books.filter((book) => book.id !== id);
        this.booksStore.set('books', filteredBooks);
        // 同时删除相关章节
        const chapters = this.chaptersStore.get('chapters');
        const filteredChapters = chapters.filter((chapter) => chapter.bookId !== id);
        this.chaptersStore.set('chapters', filteredChapters);
    }
    async getAllBooks() {
        return this.booksStore.get('books');
    }
    async getBookById(id) {
        const books = this.booksStore.get('books');
        return books.find((book) => book.id === id) || null;
    }
    // 章节管理
    async createChapter(chapterData) {
        const chapter = {
            ...chapterData,
            id: (0, uuid_1.v4)(),
            createdAt: new Date(),
            updatedAt: new Date()
        };
        const chapters = this.chaptersStore.get('chapters');
        chapters.push(chapter);
        this.chaptersStore.set('chapters', chapters);
        // 更新书籍的章节数和字数
        await this.updateBookStats(chapter.bookId);
        return chapter;
    }
    async updateChapter(id, updates) {
        const chapters = this.chaptersStore.get('chapters');
        const index = chapters.findIndex((chapter) => chapter.id === id);
        if (index === -1) {
            throw new Error(`Chapter with id ${id} not found`);
        }
        const updatedChapter = {
            ...chapters[index],
            ...updates,
            updatedAt: new Date()
        };
        chapters[index] = updatedChapter;
        this.chaptersStore.set('chapters', chapters);
        // 更新书籍统计
        await this.updateBookStats(updatedChapter.bookId);
        return updatedChapter;
    }
    async deleteChapter(id) {
        const chapters = this.chaptersStore.get('chapters');
        const chapter = chapters.find((c) => c.id === id);
        if (!chapter) {
            throw new Error(`Chapter with id ${id} not found`);
        }
        const filteredChapters = chapters.filter((chapter) => chapter.id !== id);
        this.chaptersStore.set('chapters', filteredChapters);
        // 更新书籍统计
        await this.updateBookStats(chapter.bookId);
    }
    async getChaptersByBookId(bookId) {
        const chapters = this.chaptersStore.get('chapters');
        return chapters
            .filter((chapter) => chapter.bookId === bookId)
            .sort((a, b) => a.order - b.order);
    }
    async getChapterById(id) {
        const chapters = this.chaptersStore.get('chapters');
        return chapters.find((chapter) => chapter.id === id) || null;
    }
    // 设置管理
    async getSettings() {
        return this.settingsStore.store;
    }
    async updateSettings(updates) {
        const currentSettings = this.settingsStore.store;
        const newSettings = { ...currentSettings, ...updates };
        this.settingsStore.store = newSettings;
        return newSettings;
    }
    // 私有方法：更新书籍统计信息
    async updateBookStats(bookId) {
        const chapters = await this.getChaptersByBookId(bookId);
        const wordCount = chapters.reduce((total, chapter) => total + chapter.wordCount, 0);
        const chapterCount = chapters.length;
        await this.updateBook(bookId, {
            wordCount,
            chapterCount,
            updatedAt: new Date()
        });
    }
}
exports.DataManager = DataManager;
