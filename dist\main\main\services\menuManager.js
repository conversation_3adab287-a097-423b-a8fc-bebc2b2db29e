"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MenuManager = void 0;
const electron_1 = require("electron");
class MenuManager {
    createMenu(mainWindow) {
        const template = [
            {
                label: '文件',
                submenu: [
                    {
                        label: '新建书籍',
                        accelerator: 'CmdOrCtrl+N',
                        click: () => {
                            mainWindow.webContents.send('menu:new-book');
                        }
                    },
                    {
                        label: '打开书籍',
                        accelerator: 'CmdOrCtrl+O',
                        click: async () => {
                            const result = await electron_1.dialog.showOpenDialog(mainWindow, {
                                title: '选择书籍文件',
                                filters: [
                                    { name: '文本文件', extensions: ['txt'] },
                                    { name: '所有文件', extensions: ['*'] }
                                ],
                                properties: ['openFile']
                            });
                            if (!result.canceled && result.filePaths.length > 0) {
                                mainWindow.webContents.send('menu:open-book', result.filePaths[0]);
                            }
                        }
                    },
                    { type: 'separator' },
                    {
                        label: '保存',
                        accelerator: 'CmdOrCtrl+S',
                        click: () => {
                            mainWindow.webContents.send('menu:save');
                        }
                    },
                    {
                        label: '另存为...',
                        accelerator: 'CmdOrCtrl+Shift+S',
                        click: () => {
                            mainWindow.webContents.send('menu:save-as');
                        }
                    },
                    { type: 'separator' },
                    {
                        label: '导出',
                        submenu: [
                            {
                                label: '导出为 TXT',
                                click: () => {
                                    mainWindow.webContents.send('menu:export', 'txt');
                                }
                            },
                            {
                                label: '导出为 Word',
                                click: () => {
                                    mainWindow.webContents.send('menu:export', 'docx');
                                }
                            },
                            {
                                label: '导出为 PDF',
                                click: () => {
                                    mainWindow.webContents.send('menu:export', 'pdf');
                                }
                            }
                        ]
                    },
                    { type: 'separator' },
                    {
                        label: '退出',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            electron_1.app.quit();
                        }
                    }
                ]
            },
            {
                label: '编辑',
                submenu: [
                    {
                        label: '撤销',
                        accelerator: 'CmdOrCtrl+Z',
                        role: 'undo'
                    },
                    {
                        label: '重做',
                        accelerator: 'CmdOrCtrl+Y',
                        role: 'redo'
                    },
                    { type: 'separator' },
                    {
                        label: '剪切',
                        accelerator: 'CmdOrCtrl+X',
                        role: 'cut'
                    },
                    {
                        label: '复制',
                        accelerator: 'CmdOrCtrl+C',
                        role: 'copy'
                    },
                    {
                        label: '粘贴',
                        accelerator: 'CmdOrCtrl+V',
                        role: 'paste'
                    },
                    {
                        label: '全选',
                        accelerator: 'CmdOrCtrl+A',
                        role: 'selectAll'
                    },
                    { type: 'separator' },
                    {
                        label: '查找',
                        accelerator: 'CmdOrCtrl+F',
                        click: () => {
                            mainWindow.webContents.send('menu:find');
                        }
                    },
                    {
                        label: '替换',
                        accelerator: 'CmdOrCtrl+H',
                        click: () => {
                            mainWindow.webContents.send('menu:replace');
                        }
                    }
                ]
            },
            {
                label: '视图',
                submenu: [
                    {
                        label: '书架视图',
                        accelerator: 'CmdOrCtrl+1',
                        click: () => {
                            mainWindow.webContents.send('menu:view-library');
                        }
                    },
                    {
                        label: '编辑器视图',
                        accelerator: 'CmdOrCtrl+2',
                        click: () => {
                            mainWindow.webContents.send('menu:view-editor');
                        }
                    },
                    { type: 'separator' },
                    {
                        label: '心流模式',
                        accelerator: 'F11',
                        click: () => {
                            mainWindow.webContents.send('menu:toggle-focus-mode');
                        }
                    },
                    {
                        label: '全屏',
                        accelerator: process.platform === 'darwin' ? 'Ctrl+Cmd+F' : 'F11',
                        click: () => {
                            const isFullscreen = mainWindow.isFullScreen();
                            mainWindow.setFullScreen(!isFullscreen);
                        }
                    },
                    { type: 'separator' },
                    {
                        label: '重新加载',
                        accelerator: 'CmdOrCtrl+R',
                        click: () => {
                            mainWindow.reload();
                        }
                    },
                    {
                        label: '开发者工具',
                        accelerator: process.platform === 'darwin' ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
                        click: () => {
                            mainWindow.webContents.toggleDevTools();
                        }
                    }
                ]
            },
            {
                label: '工具',
                submenu: [
                    {
                        label: '字数统计',
                        accelerator: 'CmdOrCtrl+Shift+W',
                        click: () => {
                            mainWindow.webContents.send('menu:word-count');
                        }
                    },
                    {
                        label: '设置',
                        accelerator: 'CmdOrCtrl+,',
                        click: () => {
                            mainWindow.webContents.send('menu:settings');
                        }
                    }
                ]
            },
            {
                label: '帮助',
                submenu: [
                    {
                        label: '关于',
                        click: () => {
                            electron_1.dialog.showMessageBox(mainWindow, {
                                type: 'info',
                                title: '关于 Novel Writer',
                                message: 'Novel Writer',
                                detail: '一个现代化的网文写作编辑器\n版本: 1.0.0'
                            });
                        }
                    },
                    {
                        label: '用户手册',
                        click: () => {
                            electron_1.shell.openExternal('https://github.com/novel-writer/docs');
                        }
                    },
                    {
                        label: '反馈问题',
                        click: () => {
                            electron_1.shell.openExternal('https://github.com/novel-writer/issues');
                        }
                    }
                ]
            }
        ];
        // macOS 特殊处理
        if (process.platform === 'darwin') {
            template.unshift({
                label: electron_1.app.getName(),
                submenu: [
                    { role: 'about', label: '关于 ' + electron_1.app.getName() },
                    { type: 'separator' },
                    { role: 'services', label: '服务' },
                    { type: 'separator' },
                    { role: 'hide', label: '隐藏 ' + electron_1.app.getName() },
                    { role: 'hideOthers', label: '隐藏其他' },
                    { role: 'unhide', label: '显示全部' },
                    { type: 'separator' },
                    { role: 'quit', label: '退出 ' + electron_1.app.getName() }
                ]
            });
        }
        return electron_1.Menu.buildFromTemplate(template);
    }
}
exports.MenuManager = MenuManager;
