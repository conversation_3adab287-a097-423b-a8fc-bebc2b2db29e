import Store from 'electron-store'
import { v4 as uuidv4 } from 'uuid'
import { Book, Chapter, AppSettings } from '../../shared/types'
import { DEFAULT_APP_SETTINGS } from '../../shared/constants'

export class DataManager {
  private store: Store
  private booksStore: Store<any>
  private chaptersStore: Store<any>
  private settingsStore: Store<any>

  constructor() {
    // 初始化数据存储
    this.store = new Store()
    this.booksStore = new Store({
      name: 'books',
      defaults: { books: [] }
    })
    this.chaptersStore = new Store({
      name: 'chapters', 
      defaults: { chapters: [] }
    })
    this.settingsStore = new Store({
      name: 'settings',
      defaults: DEFAULT_APP_SETTINGS
    })
  }

  async initialize(): Promise<void> {
    // 数据迁移和初始化逻辑
    console.log('DataManager initialized')
  }

  // 书籍管理
  async createBook(bookData: Omit<Book, 'id' | 'createdAt' | 'updatedAt'>): Promise<Book> {
    const book: Book = {
      ...bookData,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const books = this.booksStore.get('books')
    books.push(book)
    this.booksStore.set('books', books)

    return book
  }

  async updateBook(id: string, updates: Partial<Book>): Promise<Book> {
    const books = this.booksStore.get('books')
    const index = books.findIndex((book: any) => book.id === id)
    
    if (index === -1) {
      throw new Error(`Book with id ${id} not found`)
    }

    const updatedBook = {
      ...books[index],
      ...updates,
      updatedAt: new Date()
    }

    books[index] = updatedBook
    this.booksStore.set('books', books)

    return updatedBook
  }

  async deleteBook(id: string): Promise<void> {
    const books = this.booksStore.get('books')
    const filteredBooks = books.filter((book: any) => book.id !== id)
    this.booksStore.set('books', filteredBooks)

    // 同时删除相关章节
    const chapters = this.chaptersStore.get('chapters')
    const filteredChapters = chapters.filter((chapter: any) => chapter.bookId !== id)
    this.chaptersStore.set('chapters', filteredChapters)
  }

  async getAllBooks(): Promise<Book[]> {
    return this.booksStore.get('books')
  }

  async getBookById(id: string): Promise<Book | null> {
    const books = this.booksStore.get('books')
    return books.find((book: any) => book.id === id) || null
  }

  // 章节管理
  async createChapter(chapterData: Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'>): Promise<Chapter> {
    const chapter: Chapter = {
      ...chapterData,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const chapters = this.chaptersStore.get('chapters')
    chapters.push(chapter)
    this.chaptersStore.set('chapters', chapters)

    // 更新书籍的章节数和字数
    await this.updateBookStats(chapter.bookId)

    return chapter
  }

  async updateChapter(id: string, updates: Partial<Chapter>): Promise<Chapter> {
    const chapters = this.chaptersStore.get('chapters')
    const index = chapters.findIndex((chapter: any) => chapter.id === id)
    
    if (index === -1) {
      throw new Error(`Chapter with id ${id} not found`)
    }

    const updatedChapter = {
      ...chapters[index],
      ...updates,
      updatedAt: new Date()
    }

    chapters[index] = updatedChapter
    this.chaptersStore.set('chapters', chapters)

    // 更新书籍统计
    await this.updateBookStats(updatedChapter.bookId)

    return updatedChapter
  }

  async deleteChapter(id: string): Promise<void> {
    const chapters = this.chaptersStore.get('chapters')
    const chapter = chapters.find((c: any) => c.id === id)

    if (!chapter) {
      throw new Error(`Chapter with id ${id} not found`)
    }

    const filteredChapters = chapters.filter((chapter: any) => chapter.id !== id)
    this.chaptersStore.set('chapters', filteredChapters)

    // 更新书籍统计
    await this.updateBookStats(chapter.bookId)
  }

  async getChaptersByBookId(bookId: string): Promise<Chapter[]> {
    const chapters = this.chaptersStore.get('chapters')
    return chapters
      .filter((chapter: any) => chapter.bookId === bookId)
      .sort((a: any, b: any) => a.order - b.order)
  }

  async getChapterById(id: string): Promise<Chapter | null> {
    const chapters = this.chaptersStore.get('chapters')
    return chapters.find((chapter: any) => chapter.id === id) || null
  }

  // 设置管理
  async getSettings(): Promise<AppSettings> {
    return this.settingsStore.store as AppSettings
  }

  async updateSettings(updates: Partial<AppSettings>): Promise<AppSettings> {
    const currentSettings = this.settingsStore.store as AppSettings
    const newSettings = { ...currentSettings, ...updates } as AppSettings
    this.settingsStore.store = newSettings
    return newSettings
  }

  // 私有方法：更新书籍统计信息
  private async updateBookStats(bookId: string): Promise<void> {
    const chapters = await this.getChaptersByBookId(bookId)
    const wordCount = chapters.reduce((total, chapter) => total + chapter.wordCount, 0)
    const chapterCount = chapters.length

    await this.updateBook(bookId, {
      wordCount,
      chapterCount,
      updatedAt: new Date()
    })
  }
}
