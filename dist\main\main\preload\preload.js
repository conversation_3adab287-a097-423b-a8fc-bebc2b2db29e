"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// 创建安全的 API 对象
const electronAPI = {
    // 书籍管理
    book: {
        create: (bookData) => electron_1.ipcRenderer.invoke('book:create', bookData),
        update: (id, updates) => electron_1.ipcRenderer.invoke('book:update', id, updates),
        delete: (id) => electron_1.ipcRenderer.invoke('book:delete', id),
        getAll: () => electron_1.ipcRenderer.invoke('book:getAll'),
        getById: (id) => electron_1.ipcRenderer.invoke('book:getById', id)
    },
    // 章节管理
    chapter: {
        create: (chapterData) => electron_1.ipcRenderer.invoke('chapter:create', chapterData),
        update: (id, updates) => electron_1.ipcRenderer.invoke('chapter:update', id, updates),
        delete: (id) => electron_1.ipcRenderer.invoke('chapter:delete', id),
        getByBookId: (bookId) => electron_1.ipcRenderer.invoke('chapter:getByBookId', bookId),
        getById: (id) => electron_1.ipcRenderer.invoke('chapter:getById', id)
    },
    // 设置管理
    settings: {
        get: () => electron_1.ipcRenderer.invoke('settings:get'),
        update: (settings) => electron_1.ipcRenderer.invoke('settings:update', settings)
    },
    // 文件操作
    file: {
        export: (bookId, format) => electron_1.ipcRenderer.invoke('file:export', bookId, format),
        import: (filePath) => electron_1.ipcRenderer.invoke('file:import', filePath)
    },
    // 窗口控制
    window: {
        minimize: () => electron_1.ipcRenderer.invoke('window:minimize'),
        maximize: () => electron_1.ipcRenderer.invoke('window:maximize'),
        close: () => electron_1.ipcRenderer.invoke('window:close'),
        toggleFullscreen: () => electron_1.ipcRenderer.invoke('window:toggleFullscreen')
    },
    // 对话框
    dialog: {
        showSaveDialog: (options) => electron_1.ipcRenderer.invoke('dialog:showSaveDialog', options),
        showOpenDialog: (options) => electron_1.ipcRenderer.invoke('dialog:showOpenDialog', options)
    },
    // 菜单事件监听
    menu: {
        onNewBook: (callback) => {
            electron_1.ipcRenderer.on('menu:new-book', callback);
            return () => electron_1.ipcRenderer.removeListener('menu:new-book', callback);
        },
        onOpenBook: (callback) => {
            electron_1.ipcRenderer.on('menu:open-book', (_, filePath) => callback(filePath));
            return () => electron_1.ipcRenderer.removeListener('menu:open-book', callback);
        },
        onSave: (callback) => {
            electron_1.ipcRenderer.on('menu:save', callback);
            return () => electron_1.ipcRenderer.removeListener('menu:save', callback);
        },
        onSaveAs: (callback) => {
            electron_1.ipcRenderer.on('menu:save-as', callback);
            return () => electron_1.ipcRenderer.removeListener('menu:save-as', callback);
        },
        onExport: (callback) => {
            electron_1.ipcRenderer.on('menu:export', (_, format) => callback(format));
            return () => electron_1.ipcRenderer.removeListener('menu:export', callback);
        },
        onFind: (callback) => {
            electron_1.ipcRenderer.on('menu:find', callback);
            return () => electron_1.ipcRenderer.removeListener('menu:find', callback);
        },
        onReplace: (callback) => {
            electron_1.ipcRenderer.on('menu:replace', callback);
            return () => electron_1.ipcRenderer.removeListener('menu:replace', callback);
        },
        onViewLibrary: (callback) => {
            electron_1.ipcRenderer.on('menu:view-library', callback);
            return () => electron_1.ipcRenderer.removeListener('menu:view-library', callback);
        },
        onViewEditor: (callback) => {
            electron_1.ipcRenderer.on('menu:view-editor', callback);
            return () => electron_1.ipcRenderer.removeListener('menu:view-editor', callback);
        },
        onToggleFocusMode: (callback) => {
            electron_1.ipcRenderer.on('menu:toggle-focus-mode', callback);
            return () => electron_1.ipcRenderer.removeListener('menu:toggle-focus-mode', callback);
        },
        onWordCount: (callback) => {
            electron_1.ipcRenderer.on('menu:word-count', callback);
            return () => electron_1.ipcRenderer.removeListener('menu:word-count', callback);
        },
        onSettings: (callback) => {
            electron_1.ipcRenderer.on('menu:settings', callback);
            return () => electron_1.ipcRenderer.removeListener('menu:settings', callback);
        }
    }
};
// 暴露 API 到渲染进程
electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
